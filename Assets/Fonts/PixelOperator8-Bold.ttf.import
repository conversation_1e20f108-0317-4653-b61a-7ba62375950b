[remap]

importer="font_data_dynamic"
type="FontFile"
uid="uid://cbowgwltqkamb"
path="res://.godot/imported/PixelOperator8-Bold.ttf-ae4447479e63d90bea7c70a3728facd1.fontdata"

[deps]

source_file="res://Assets/Fonts/PixelOperator8-Bold.ttf"
dest_files=["res://.godot/imported/PixelOperator8-Bold.ttf-ae4447479e63d90bea7c70a3728facd1.fontdata"]

[params]

Rendering=null
antialiasing=1
generate_mipmaps=false
disable_embedded_bitmaps=true
multichannel_signed_distance_field=false
msdf_pixel_range=8
msdf_size=48
allow_system_fallback=true
force_autohinter=false
hinting=1
subpixel_positioning=1
keep_rounding_remainders=true
oversampling=0.0
Fallbacks=null
fallbacks=[]
Compress=null
compress=true
preload=[]
language_support={}
script_support={}
opentype_features={}

[gd_resource type="TileSet" load_steps=13 format=3 uid="uid://6386asadi854"]

[ext_resource type="Texture2D" uid="uid://clox863hbl6dx" path="res://Assets/Tiles/Kenney1Bit-Monochrome-Transparent.png" id="1_rv4e1"]

[sub_resource type="OccluderPolygon2D" id="OccluderPolygon2D_bojn3"]
polygon = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)

[sub_resource type="OccluderPolygon2D" id="OccluderPolygon2D_2h6ev"]
polygon = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)

[sub_resource type="OccluderPolygon2D" id="OccluderPolygon2D_udp1x"]
polygon = PackedVector2Array(-6, -4, 8, -4, 8, 2, -6, 2)

[sub_resource type="OccluderPolygon2D" id="OccluderPolygon2D_dq1fv"]
polygon = PackedVector2Array(8, -8, 8, 8, -8, 8)

[sub_resource type="OccluderPolygon2D" id="OccluderPolygon2D_0b2md"]
polygon = PackedVector2Array(-8, -4, 8, -4, 8, 2, -8, 2)

[sub_resource type="OccluderPolygon2D" id="OccluderPolygon2D_3ca6o"]
polygon = PackedVector2Array(8, -8, 8, 8, -8, 8)

[sub_resource type="OccluderPolygon2D" id="OccluderPolygon2D_4stdy"]
polygon = PackedVector2Array(-8, -8, 8, -8, 8, 8)

[sub_resource type="OccluderPolygon2D" id="OccluderPolygon2D_mwv7i"]
polygon = PackedVector2Array(8, 8, -8, 8, -8, -8)

[sub_resource type="OccluderPolygon2D" id="OccluderPolygon2D_smig3"]
polygon = PackedVector2Array(-8, 8, -8, -8, 8, -8)

[sub_resource type="OccluderPolygon2D" id="OccluderPolygon2D_ijyqe"]
polygon = PackedVector2Array(-8, -8, 8, 8, -8, 8)

[sub_resource type="TileSetAtlasSource" id="TileSetAtlasSource_ay4j7"]
resource_name = "Monochrome-Transparent-Physics"
texture = ExtResource("1_rv4e1")
0:0/next_alternative_id = 2
0:0/0 = 0
0:0/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_bojn3")
0:0/0/custom_data_0 = true
0:0/1 = 1
1:0/0 = 0
1:0/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
1:0/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
1:0/0/custom_data_0 = true
2:0/0 = 0
2:0/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
2:0/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
2:0/0/custom_data_0 = true
3:0/0 = 0
3:0/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
3:0/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
3:0/0/custom_data_0 = true
4:0/0 = 0
4:0/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
4:0/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
4:0/0/custom_data_0 = true
5:0/0 = 0
5:0/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
5:0/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
5:0/0/custom_data_0 = true
6:0/0 = 0
6:0/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
6:0/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
6:0/0/custom_data_0 = true
7:0/0 = 0
7:0/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
7:0/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
7:0/0/custom_data_0 = true
8:0/0 = 0
8:0/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
8:0/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
8:0/0/custom_data_0 = true
9:0/0 = 0
9:0/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
9:0/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
9:0/0/custom_data_0 = true
10:0/0 = 0
10:0/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
10:0/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
10:0/0/custom_data_0 = true
11:0/0 = 0
11:0/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
11:0/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
11:0/0/custom_data_0 = true
12:0/0 = 0
12:0/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
12:0/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
12:0/0/custom_data_0 = true
13:0/0 = 0
13:0/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
13:0/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
13:0/0/custom_data_0 = true
14:0/0 = 0
14:0/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
14:0/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
14:0/0/custom_data_0 = true
15:0/0 = 0
15:0/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
15:0/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
15:0/0/custom_data_0 = true
16:0/0 = 0
16:0/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
16:0/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
16:0/0/custom_data_1 = true
17:0/0 = 0
17:0/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
17:0/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
17:0/0/custom_data_1 = true
18:0/0 = 0
18:0/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
18:0/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
18:0/0/custom_data_1 = true
19:0/0 = 0
19:0/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
19:0/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
19:0/0/custom_data_1 = true
20:0/0 = 0
20:0/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
20:0/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
20:0/0/custom_data_1 = true
21:0/0 = 0
21:0/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
21:0/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
21:0/0/custom_data_0 = true
22:0/0 = 0
22:0/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
22:0/0/physics_layer_0/polygon_0/points = PackedVector2Array(8, 0, 8, 8, -8, 8, -8, 0)
22:0/0/custom_data_1 = true
23:0/0 = 0
23:0/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
23:0/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
23:0/0/custom_data_1 = true
24:0/0 = 0
24:0/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
24:0/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
24:0/0/custom_data_1 = true
25:0/0 = 0
25:0/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
25:0/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
25:0/0/custom_data_1 = true
26:0/0 = 0
26:0/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
26:0/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
26:0/0/custom_data_1 = true
27:0/0 = 0
27:0/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
27:0/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
27:0/0/custom_data_1 = true
28:0/0 = 0
28:0/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
28:0/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
28:0/0/custom_data_1 = true
29:0/0 = 0
29:0/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
29:0/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
29:0/0/custom_data_1 = true
30:0/0 = 0
30:0/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
30:0/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
30:0/0/custom_data_1 = true
31:0/0 = 0
31:0/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
31:0/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
31:0/0/custom_data_1 = true
32:0/0 = 0
32:0/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
32:0/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
32:0/0/custom_data_1 = true
33:0/0 = 0
33:0/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
33:0/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
33:0/0/custom_data_1 = true
34:0/0 = 0
34:0/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
34:0/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
34:0/0/custom_data_1 = true
35:0/0 = 0
35:0/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
35:0/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
35:0/0/custom_data_1 = true
36:0/0 = 0
36:0/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
36:0/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
36:0/0/custom_data_1 = true
37:0/0 = 0
37:0/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
37:0/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
37:0/0/custom_data_1 = true
38:0/0 = 0
38:0/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
38:0/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
38:0/0/custom_data_1 = true
39:0/0 = 0
39:0/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
39:0/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
39:0/0/custom_data_1 = true
40:0/0 = 0
40:0/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
40:0/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
40:0/0/custom_data_1 = true
41:0/0 = 0
41:0/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
41:0/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
41:0/0/custom_data_1 = true
42:0/0 = 0
42:0/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
42:0/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
42:0/0/custom_data_1 = true
43:0/0 = 0
43:0/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
43:0/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
43:0/0/custom_data_1 = true
44:0/0 = 0
44:0/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
44:0/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
44:0/0/custom_data_1 = true
45:0/0 = 0
45:0/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
45:0/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
45:0/0/custom_data_1 = true
46:0/0 = 0
46:0/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
46:0/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
46:0/0/custom_data_1 = true
47:0/0 = 0
47:0/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
47:0/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
47:0/0/custom_data_1 = true
48:0/0 = 0
48:0/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
48:0/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
48:0/0/custom_data_1 = true
0:1/0 = 0
0:1/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
0:1/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
0:1/0/custom_data_1 = true
1:1/0 = 0
1:1/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
1:1/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
1:1/0/custom_data_1 = true
2:1/0 = 0
2:1/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
2:1/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
2:1/0/custom_data_1 = true
3:1/0 = 0
3:1/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
3:1/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
3:1/0/custom_data_1 = true
4:1/0 = 0
4:1/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
4:1/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
4:1/0/custom_data_1 = true
5:1/0 = 0
5:1/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
5:1/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
5:1/0/custom_data_1 = true
6:1/0 = 0
6:1/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
6:1/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
6:1/0/custom_data_1 = true
7:1/0 = 0
7:1/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
7:1/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
7:1/0/custom_data_1 = true
8:1/0 = 0
8:1/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
8:1/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
8:1/0/custom_data_0 = true
9:1/0 = 0
9:1/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
9:1/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
9:1/0/custom_data_0 = true
10:1/0 = 0
10:1/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
10:1/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
10:1/0/custom_data_0 = true
11:1/0 = 0
11:1/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
11:1/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
11:1/0/custom_data_0 = true
12:1/0 = 0
12:1/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
12:1/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
12:1/0/custom_data_0 = true
13:1/0 = 0
13:1/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
13:1/0/custom_data_0 = true
14:1/0 = 0
14:1/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
14:1/0/custom_data_0 = true
15:1/0 = 0
15:1/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
16:1/0 = 0
16:1/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
16:1/0/custom_data_0 = true
17:1/0 = 0
17:1/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
17:1/0/custom_data_0 = true
18:1/0 = 0
18:1/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
18:1/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
18:1/0/custom_data_1 = true
19:1/0 = 0
19:1/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
19:1/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
19:1/0/custom_data_1 = true
20:1/0 = 0
20:1/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
20:1/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
20:1/0/custom_data_1 = true
21:1/0 = 0
21:1/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
21:1/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
21:1/0/custom_data_0 = true
22:1/0 = 0
22:1/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
22:1/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
22:1/0/custom_data_1 = true
23:1/0 = 0
23:1/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
23:1/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
23:1/0/custom_data_1 = true
24:1/0 = 0
24:1/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
24:1/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
24:1/0/custom_data_1 = true
25:1/0 = 0
25:1/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
25:1/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
25:1/0/custom_data_1 = true
26:1/0 = 0
26:1/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
26:1/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
26:1/0/custom_data_1 = true
27:1/0 = 0
27:1/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
27:1/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
27:1/0/custom_data_1 = true
28:1/0 = 0
28:1/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
28:1/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
28:1/0/custom_data_1 = true
29:1/0 = 0
29:1/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
29:1/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
29:1/0/custom_data_1 = true
30:1/0 = 0
30:1/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
30:1/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
30:1/0/custom_data_1 = true
31:1/0 = 0
31:1/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
31:1/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
31:1/0/custom_data_1 = true
32:1/0 = 0
32:1/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
32:1/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
32:1/0/custom_data_1 = true
33:1/0 = 0
33:1/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
33:1/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
33:1/0/custom_data_1 = true
34:1/0 = 0
34:1/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
34:1/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
34:1/0/custom_data_1 = true
35:1/0 = 0
35:1/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
35:1/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
35:1/0/custom_data_1 = true
36:1/0 = 0
36:1/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
36:1/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
36:1/0/custom_data_1 = true
37:1/0 = 0
37:1/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
37:1/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
37:1/0/custom_data_1 = true
38:1/0 = 0
38:1/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
38:1/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
38:1/0/custom_data_1 = true
39:1/0 = 0
39:1/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
39:1/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
39:1/0/custom_data_1 = true
40:1/0 = 0
40:1/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
40:1/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
40:1/0/custom_data_1 = true
41:1/0 = 0
41:1/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
41:1/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
41:1/0/custom_data_1 = true
42:1/0 = 0
42:1/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
42:1/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
42:1/0/custom_data_1 = true
43:1/0 = 0
43:1/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
43:1/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
43:1/0/custom_data_1 = true
44:1/0 = 0
44:1/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
44:1/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
44:1/0/custom_data_1 = true
45:1/0 = 0
45:1/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
45:1/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
45:1/0/custom_data_1 = true
46:1/0 = 0
46:1/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
46:1/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
46:1/0/custom_data_1 = true
47:1/0 = 0
47:1/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
47:1/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
47:1/0/custom_data_1 = true
48:1/0 = 0
48:1/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
48:1/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
48:1/0/custom_data_1 = true
0:2/0 = 0
0:2/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
0:2/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
0:2/0/custom_data_1 = true
1:2/0 = 0
1:2/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
1:2/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
1:2/0/custom_data_1 = true
2:2/0 = 0
2:2/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
2:2/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
2:2/0/custom_data_1 = true
3:2/0 = 0
3:2/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
3:2/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
3:2/0/custom_data_1 = true
4:2/0 = 0
4:2/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
4:2/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
4:2/0/custom_data_1 = true
5:2/0 = 0
5:2/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
5:2/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
5:2/0/custom_data_1 = true
6:2/0 = 0
6:2/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
6:2/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
6:2/0/custom_data_1 = true
7:2/0 = 0
7:2/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
7:2/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
7:2/0/custom_data_1 = true
8:2/0 = 0
8:2/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
8:2/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
8:2/0/custom_data_0 = true
9:2/0 = 0
9:2/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
9:2/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
9:2/0/custom_data_0 = true
10:2/0 = 0
10:2/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
10:2/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
10:2/0/custom_data_0 = true
11:2/0 = 0
11:2/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
11:2/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
11:2/0/custom_data_0 = true
12:2/0 = 0
12:2/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
12:2/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
12:2/0/custom_data_0 = true
13:2/0 = 0
13:2/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
13:2/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
13:2/0/custom_data_0 = true
14:2/0 = 0
14:2/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
14:2/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
14:2/0/custom_data_0 = true
15:2/0 = 0
15:2/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
15:2/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
15:2/0/custom_data_0 = true
16:2/0 = 0
16:2/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
16:2/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
16:2/0/custom_data_0 = true
17:2/0 = 0
17:2/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
17:2/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
17:2/0/custom_data_0 = true
18:2/0 = 0
18:2/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
18:2/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
18:2/0/custom_data_1 = true
19:2/0 = 0
19:2/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
19:2/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
19:2/0/custom_data_1 = true
20:2/0 = 0
20:2/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
20:2/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
20:2/0/custom_data_1 = true
21:2/0 = 0
21:2/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
21:2/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
21:2/0/custom_data_0 = true
21:2/0/custom_data_1 = true
22:2/0 = 0
22:2/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
22:2/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
22:2/0/custom_data_1 = true
23:2/0 = 0
23:2/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
23:2/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
23:2/0/custom_data_1 = true
24:2/0 = 0
24:2/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
24:2/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
24:2/0/custom_data_1 = true
25:2/0 = 0
25:2/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
25:2/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
25:2/0/custom_data_1 = true
26:2/0 = 0
26:2/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
26:2/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
26:2/0/custom_data_1 = true
27:2/0 = 0
27:2/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
27:2/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
27:2/0/custom_data_1 = true
28:2/0 = 0
28:2/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
28:2/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
28:2/0/custom_data_1 = true
29:2/0 = 0
29:2/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
29:2/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
29:2/0/custom_data_1 = true
30:2/0 = 0
30:2/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
30:2/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
30:2/0/custom_data_1 = true
31:2/0 = 0
31:2/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
31:2/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
31:2/0/custom_data_1 = true
32:2/0 = 0
32:2/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
32:2/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
32:2/0/custom_data_1 = true
33:2/0 = 0
33:2/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
33:2/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
33:2/0/custom_data_1 = true
34:2/0 = 0
34:2/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
34:2/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
34:2/0/custom_data_1 = true
35:2/0 = 0
35:2/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
35:2/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
35:2/0/custom_data_1 = true
36:2/0 = 0
36:2/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
36:2/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
36:2/0/custom_data_1 = true
37:2/0 = 0
37:2/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
37:2/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
37:2/0/custom_data_1 = true
38:2/0 = 0
38:2/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
38:2/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
38:2/0/custom_data_1 = true
39:2/0 = 0
39:2/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
39:2/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
39:2/0/custom_data_1 = true
40:2/0 = 0
40:2/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
40:2/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
40:2/0/custom_data_1 = true
41:2/0 = 0
41:2/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
41:2/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
41:2/0/custom_data_1 = true
42:2/0 = 0
42:2/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
42:2/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
42:2/0/custom_data_1 = true
43:2/0 = 0
43:2/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
43:2/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
43:2/0/custom_data_1 = true
44:2/0 = 0
44:2/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
44:2/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
44:2/0/custom_data_1 = true
45:2/0 = 0
45:2/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
45:2/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
45:2/0/custom_data_1 = true
46:2/0 = 0
46:2/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
46:2/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
46:2/0/custom_data_1 = true
47:2/0 = 0
47:2/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
47:2/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
47:2/0/custom_data_1 = true
48:2/0 = 0
48:2/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
48:2/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
48:2/0/custom_data_1 = true
0:3/0 = 0
0:3/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
0:3/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
0:3/0/custom_data_1 = true
1:3/0 = 0
1:3/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
1:3/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
1:3/0/custom_data_1 = true
2:3/0 = 0
2:3/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
2:3/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
2:3/0/custom_data_1 = true
3:3/0 = 0
3:3/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
3:3/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
3:3/0/custom_data_1 = true
4:3/0 = 0
4:3/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
4:3/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
4:3/0/custom_data_1 = true
5:3/0 = 0
5:3/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
5:3/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
5:3/0/custom_data_0 = true
6:3/0 = 0
6:3/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
6:3/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
6:3/0/custom_data_0 = true
7:3/0 = 0
7:3/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
7:3/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
8:3/0 = 0
8:3/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
8:3/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
9:3/0 = 0
9:3/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
9:3/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
10:3/0 = 0
10:3/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
10:3/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
11:3/0 = 0
11:3/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
11:3/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
12:3/0 = 0
12:3/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
12:3/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
13:3/0 = 0
13:3/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
13:3/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
13:3/0/custom_data_0 = true
14:3/0 = 0
14:3/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
14:3/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
14:3/0/custom_data_0 = true
15:3/0 = 0
15:3/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
15:3/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
15:3/0/custom_data_0 = true
16:3/0 = 0
16:3/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
16:3/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
16:3/0/custom_data_0 = true
17:3/0 = 0
17:3/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
17:3/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
17:3/0/custom_data_0 = true
18:3/0 = 0
18:3/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
18:3/0/physics_layer_0/polygon_0/points = PackedVector2Array(2, 2, 8, 2, 8, 8, 2, 8)
18:3/0/custom_data_0 = true
19:3/0 = 0
19:3/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
19:3/0/physics_layer_0/polygon_0/points = PackedVector2Array(-2, 2, -8, 2, -8, 8, 0, 8, 0, 6, -2, 6)
19:3/0/custom_data_0 = true
20:3/0 = 0
20:3/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_3ca6o")
20:3/0/physics_layer_0/polygon_0/points = PackedVector2Array(8, -8, 8, 8, -8, 8)
20:3/0/custom_data_1 = true
21:3/0 = 0
21:3/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_mwv7i")
21:3/0/physics_layer_0/polygon_0/points = PackedVector2Array(8, 8, -8, 8, -8, -8)
21:3/0/custom_data_1 = true
22:3/0 = 0
22:3/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
22:3/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
22:3/0/custom_data_1 = true
23:3/0 = 0
23:3/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
23:3/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
23:3/0/custom_data_1 = true
24:3/0 = 0
24:3/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
24:3/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
24:3/0/custom_data_1 = true
25:3/0 = 0
25:3/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
25:3/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
25:3/0/custom_data_1 = true
26:3/0 = 0
26:3/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
26:3/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
26:3/0/custom_data_1 = true
27:3/0 = 0
27:3/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
27:3/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
27:3/0/custom_data_1 = true
28:3/0 = 0
28:3/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
28:3/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
28:3/0/custom_data_1 = true
29:3/0 = 0
29:3/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
29:3/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
29:3/0/custom_data_1 = true
30:3/0 = 0
30:3/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
30:3/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
30:3/0/custom_data_1 = true
31:3/0 = 0
31:3/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
31:3/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
31:3/0/custom_data_1 = true
32:3/0 = 0
32:3/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
32:3/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
32:3/0/custom_data_1 = true
33:3/0 = 0
33:3/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
33:3/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
33:3/0/custom_data_1 = true
34:3/0 = 0
34:3/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
34:3/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
34:3/0/custom_data_1 = true
35:3/0 = 0
35:3/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
35:3/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
35:3/0/custom_data_1 = true
36:3/0 = 0
36:3/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
36:3/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
36:3/0/custom_data_1 = true
37:3/0 = 0
37:3/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
37:3/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
37:3/0/custom_data_1 = true
38:3/0 = 0
38:3/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
38:3/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
38:3/0/custom_data_1 = true
39:3/0 = 0
39:3/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
39:3/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
39:3/0/custom_data_1 = true
40:3/0 = 0
40:3/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
40:3/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
40:3/0/custom_data_1 = true
41:3/0 = 0
41:3/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
41:3/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
41:3/0/custom_data_1 = true
42:3/0 = 0
42:3/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
42:3/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
42:3/0/custom_data_1 = true
43:3/0 = 0
43:3/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
43:3/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
43:3/0/custom_data_1 = true
44:3/0 = 0
44:3/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
44:3/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
44:3/0/custom_data_1 = true
45:3/0 = 0
45:3/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
45:3/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
45:3/0/custom_data_1 = true
46:3/0 = 0
46:3/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
46:3/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
46:3/0/custom_data_1 = true
47:3/0 = 0
47:3/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
47:3/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
47:3/0/custom_data_1 = true
48:3/0 = 0
48:3/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
48:3/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
48:3/0/custom_data_1 = true
0:4/0 = 0
0:4/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
0:4/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
0:4/0/custom_data_1 = true
1:4/0 = 0
1:4/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
1:4/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
1:4/0/custom_data_1 = true
2:4/0 = 0
2:4/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
2:4/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
2:4/0/custom_data_1 = true
3:4/0 = 0
3:4/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
3:4/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
3:4/0/custom_data_1 = true
4:4/0 = 0
4:4/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
4:4/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
4:4/0/custom_data_1 = true
5:4/0 = 0
5:4/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
5:4/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
5:4/0/custom_data_0 = true
6:4/0 = 0
6:4/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
6:4/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
6:4/0/custom_data_1 = true
7:4/0 = 0
7:4/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
7:4/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
7:4/0/custom_data_1 = true
8:4/0 = 0
8:4/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
8:4/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
8:4/0/custom_data_1 = true
9:4/0 = 0
9:4/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
9:4/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
9:4/0/custom_data_1 = true
10:4/0 = 0
10:4/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
10:4/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
10:4/0/custom_data_1 = true
11:4/0 = 0
11:4/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
11:4/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
11:4/0/custom_data_1 = true
12:4/0 = 0
12:4/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
12:4/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
12:4/0/custom_data_1 = true
13:4/0 = 0
13:4/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
13:4/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
13:4/0/custom_data_0 = true
14:4/0 = 0
14:4/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
14:4/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
14:4/0/custom_data_0 = true
15:4/0 = 0
15:4/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
15:4/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
15:4/0/custom_data_0 = true
16:4/0 = 0
16:4/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
16:4/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
16:4/0/custom_data_0 = true
17:4/0 = 0
17:4/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
17:4/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
17:4/0/custom_data_0 = true
18:4/0 = 0
18:4/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
18:4/0/physics_layer_0/polygon_0/points = PackedVector2Array(2, -2, 8, -2, 8, -8, 2, -8)
18:4/0/custom_data_0 = true
19:4/0 = 0
19:4/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
19:4/0/physics_layer_0/polygon_0/points = PackedVector2Array(-2, -2, -8, -2, -8, -8, 0, -8, 0, -6, -2, -6)
19:4/0/custom_data_0 = true
20:4/0 = 0
20:4/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_4stdy")
20:4/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8)
20:4/0/custom_data_1 = true
21:4/0 = 0
21:4/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_smig3")
21:4/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, 8, -8, -8, 8, -8)
21:4/0/custom_data_1 = true
22:4/0 = 0
22:4/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
22:4/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
22:4/0/custom_data_1 = true
23:4/0 = 0
23:4/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
23:4/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
23:4/0/custom_data_1 = true
24:4/0 = 0
24:4/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
24:4/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
24:4/0/custom_data_1 = true
25:4/0 = 0
25:4/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
25:4/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
25:4/0/custom_data_1 = true
26:4/0 = 0
26:4/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
26:4/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
26:4/0/custom_data_1 = true
27:4/0 = 0
27:4/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
27:4/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
27:4/0/custom_data_1 = true
28:4/0 = 0
28:4/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
28:4/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
28:4/0/custom_data_1 = true
29:4/0 = 0
29:4/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
29:4/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
29:4/0/custom_data_1 = true
30:4/0 = 0
30:4/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
30:4/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
30:4/0/custom_data_1 = true
31:4/0 = 0
31:4/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
31:4/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
31:4/0/custom_data_1 = true
32:4/0 = 0
32:4/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
32:4/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
32:4/0/custom_data_1 = true
33:4/0 = 0
33:4/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
33:4/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
33:4/0/custom_data_1 = true
34:4/0 = 0
34:4/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
34:4/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
34:4/0/custom_data_1 = true
35:4/0 = 0
35:4/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
35:4/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
35:4/0/custom_data_1 = true
36:4/0 = 0
36:4/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
36:4/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
36:4/0/custom_data_1 = true
37:4/0 = 0
37:4/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
37:4/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
37:4/0/custom_data_1 = true
38:4/0 = 0
38:4/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
38:4/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
38:4/0/custom_data_1 = true
39:4/0 = 0
39:4/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
39:4/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
39:4/0/custom_data_1 = true
40:4/0 = 0
40:4/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
40:4/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
40:4/0/custom_data_1 = true
41:4/0 = 0
41:4/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
41:4/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
41:4/0/custom_data_1 = true
42:4/0 = 0
42:4/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
42:4/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
42:4/0/custom_data_1 = true
43:4/0 = 0
43:4/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
43:4/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
43:4/0/custom_data_1 = true
44:4/0 = 0
44:4/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
44:4/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
44:4/0/custom_data_1 = true
45:4/0 = 0
45:4/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
45:4/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
45:4/0/custom_data_1 = true
46:4/0 = 0
46:4/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
46:4/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
46:4/0/custom_data_1 = true
47:4/0 = 0
47:4/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
47:4/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
47:4/0/custom_data_1 = true
48:4/0 = 0
48:4/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
48:4/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
48:4/0/custom_data_1 = true
0:5/0 = 0
0:5/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
0:5/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
0:5/0/custom_data_1 = true
1:5/0 = 0
1:5/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
1:5/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
1:5/0/custom_data_1 = true
2:5/0 = 0
2:5/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
2:5/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
2:5/0/custom_data_1 = true
3:5/0 = 0
3:5/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
3:5/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
3:5/0/custom_data_1 = true
4:5/0 = 0
4:5/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
4:5/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
4:5/0/custom_data_1 = true
5:5/0 = 0
5:5/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
5:5/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
5:5/0/custom_data_1 = true
6:5/0 = 0
6:5/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
6:5/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
6:5/0/custom_data_1 = true
7:5/0 = 0
7:5/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
7:5/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
7:5/0/custom_data_1 = true
8:5/0 = 0
8:5/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
8:5/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
8:5/0/custom_data_1 = true
9:5/0 = 0
9:5/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
9:5/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
9:5/0/custom_data_1 = true
10:5/0 = 0
10:5/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
10:5/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
10:5/0/custom_data_1 = true
11:5/0 = 0
11:5/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
11:5/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
11:5/0/custom_data_1 = true
12:5/0 = 0
12:5/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
12:5/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
12:5/0/custom_data_1 = true
13:5/0 = 0
13:5/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
13:5/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
13:5/0/custom_data_1 = true
14:5/0 = 0
14:5/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
14:5/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
14:5/0/custom_data_1 = true
15:5/0 = 0
15:5/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
15:5/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
15:5/0/custom_data_1 = true
16:5/0 = 0
16:5/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
16:5/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
16:5/0/custom_data_1 = true
17:5/0 = 0
17:5/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
17:5/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
17:5/0/custom_data_1 = true
18:5/0 = 0
18:5/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
18:5/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
18:5/0/custom_data_1 = true
19:5/0 = 0
19:5/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
19:5/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
19:5/0/custom_data_1 = true
20:5/0 = 0
20:5/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
20:5/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
20:5/0/custom_data_1 = true
21:5/0 = 0
21:5/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
21:5/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
21:5/0/custom_data_1 = true
22:5/0 = 0
22:5/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
22:5/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
22:5/0/custom_data_1 = true
23:5/0 = 0
23:5/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
23:5/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
23:5/0/custom_data_1 = true
24:5/0 = 0
24:5/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
24:5/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
24:5/0/custom_data_1 = true
25:5/0 = 0
25:5/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
25:5/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
25:5/0/custom_data_1 = true
26:5/0 = 0
26:5/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
26:5/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
26:5/0/custom_data_1 = true
27:5/0 = 0
27:5/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
27:5/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
27:5/0/custom_data_1 = true
28:5/0 = 0
28:5/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
28:5/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
28:5/0/custom_data_1 = true
29:5/0 = 0
29:5/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
29:5/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
29:5/0/custom_data_1 = true
30:5/0 = 0
30:5/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
30:5/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
30:5/0/custom_data_1 = true
31:5/0 = 0
31:5/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
31:5/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
31:5/0/custom_data_1 = true
32:5/0 = 0
32:5/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
32:5/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
32:5/0/custom_data_1 = true
33:5/0 = 0
33:5/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
33:5/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
33:5/0/custom_data_1 = true
34:5/0 = 0
34:5/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
34:5/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
34:5/0/custom_data_1 = true
35:5/0 = 0
35:5/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
35:5/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
35:5/0/custom_data_1 = true
36:5/0 = 0
36:5/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
36:5/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
36:5/0/custom_data_1 = true
37:5/0 = 0
37:5/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
37:5/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
37:5/0/custom_data_1 = true
38:5/0 = 0
38:5/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
38:5/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
38:5/0/custom_data_1 = true
39:5/0 = 0
39:5/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
39:5/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
39:5/0/custom_data_1 = true
40:5/0 = 0
40:5/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
40:5/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
40:5/0/custom_data_1 = true
41:5/0 = 0
41:5/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
41:5/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
41:5/0/custom_data_1 = true
42:5/0 = 0
42:5/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
42:5/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
42:5/0/custom_data_1 = true
43:5/0 = 0
43:5/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
43:5/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
43:5/0/custom_data_1 = true
44:5/0 = 0
44:5/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
44:5/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
44:5/0/custom_data_1 = true
45:5/0 = 0
45:5/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
45:5/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
45:5/0/custom_data_1 = true
46:5/0 = 0
46:5/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
46:5/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
46:5/0/custom_data_1 = true
47:5/0 = 0
47:5/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
47:5/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
47:5/0/custom_data_1 = true
48:5/0 = 0
48:5/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
48:5/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
48:5/0/custom_data_1 = true
0:6/0 = 0
0:6/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
0:6/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
0:6/0/custom_data_1 = true
1:6/0 = 0
1:6/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
1:6/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
1:6/0/custom_data_1 = true
2:6/0 = 0
2:6/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_ijyqe")
2:6/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, 8, -8, 8)
2:6/0/custom_data_1 = true
3:6/0 = 0
3:6/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
3:6/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
3:6/0/custom_data_1 = true
4:6/0 = 0
4:6/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
4:6/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
4:6/0/custom_data_1 = true
5:6/0 = 0
5:6/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
5:6/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
5:6/0/custom_data_1 = true
6:6/0 = 0
6:6/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
6:6/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
6:6/0/custom_data_1 = true
7:6/0 = 0
7:6/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
7:6/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
7:6/0/custom_data_1 = true
8:6/0 = 0
8:6/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
8:6/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
8:6/0/custom_data_1 = true
9:6/0 = 0
9:6/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
9:6/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
9:6/0/custom_data_1 = true
10:6/0 = 0
10:6/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
10:6/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
10:6/0/custom_data_1 = true
11:6/0 = 0
11:6/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
11:6/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
11:6/0/custom_data_1 = true
12:6/0 = 0
12:6/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
12:6/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
12:6/0/custom_data_1 = true
13:6/0 = 0
13:6/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
13:6/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
13:6/0/custom_data_1 = true
14:6/0 = 0
14:6/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
14:6/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
14:6/0/custom_data_1 = true
15:6/0 = 0
15:6/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
15:6/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
15:6/0/custom_data_1 = true
16:6/0 = 0
16:6/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
16:6/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
16:6/0/custom_data_1 = true
17:6/0 = 0
17:6/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
17:6/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
17:6/0/custom_data_1 = true
18:6/0 = 0
18:6/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
18:6/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
18:6/0/custom_data_1 = true
19:6/0 = 0
19:6/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
19:6/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
19:6/0/custom_data_1 = true
20:6/0 = 0
20:6/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
20:6/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
20:6/0/custom_data_1 = true
21:6/0 = 0
21:6/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
21:6/0/physics_layer_0/polygon_0/points = PackedVector2Array(8, -8, 8, -2, -2, -2, -8, -8)
21:6/0/physics_layer_0/polygon_0/one_way = true
21:6/0/custom_data_1 = true
22:6/0 = 0
22:6/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
22:6/0/physics_layer_0/polygon_0/points = PackedVector2Array(8, -8, 8, -2, -8, -2, -8, -8)
22:6/0/physics_layer_0/polygon_0/one_way = true
22:6/0/custom_data_1 = true
23:6/0 = 0
23:6/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
23:6/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, -8, -2, 2, -2, 8, -8)
23:6/0/physics_layer_0/polygon_0/one_way = true
23:6/0/custom_data_1 = true
24:6/0 = 0
24:6/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
24:6/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
24:6/0/custom_data_1 = true
25:6/0 = 0
25:6/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
25:6/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
25:6/0/custom_data_1 = true
26:6/0 = 0
26:6/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
26:6/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
26:6/0/custom_data_1 = true
27:6/0 = 0
27:6/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
27:6/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
27:6/0/custom_data_1 = true
28:6/0 = 0
28:6/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
28:6/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
28:6/0/custom_data_1 = true
29:6/0 = 0
29:6/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
29:6/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
29:6/0/custom_data_1 = true
30:6/0 = 0
30:6/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
30:6/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
30:6/0/custom_data_1 = true
31:6/0 = 0
31:6/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
31:6/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
31:6/0/custom_data_1 = true
32:6/0 = 0
32:6/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
32:6/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
32:6/0/custom_data_1 = true
33:6/0 = 0
33:6/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
33:6/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
33:6/0/custom_data_1 = true
34:6/0 = 0
34:6/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
34:6/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
34:6/0/custom_data_1 = true
35:6/0 = 0
35:6/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
35:6/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
35:6/0/custom_data_1 = true
36:6/0 = 0
36:6/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
36:6/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
36:6/0/custom_data_1 = true
37:6/0 = 0
37:6/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
37:6/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
37:6/0/custom_data_1 = true
38:6/0 = 0
38:6/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
38:6/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
38:6/0/custom_data_1 = true
39:6/0 = 0
39:6/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
39:6/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
39:6/0/custom_data_1 = true
40:6/0 = 0
40:6/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
40:6/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
40:6/0/custom_data_1 = true
41:6/0 = 0
41:6/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
41:6/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
41:6/0/custom_data_1 = true
42:6/0 = 0
42:6/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
42:6/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
42:6/0/custom_data_1 = true
43:6/0 = 0
43:6/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
43:6/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
43:6/0/custom_data_1 = true
44:6/0 = 0
44:6/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
44:6/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
44:6/0/custom_data_1 = true
45:6/0 = 0
45:6/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
45:6/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
45:6/0/custom_data_1 = true
46:6/0 = 0
46:6/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
46:6/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
46:6/0/custom_data_1 = true
47:6/0 = 0
47:6/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
47:6/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
47:6/0/custom_data_1 = true
48:6/0 = 0
48:6/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
48:6/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
48:6/0/custom_data_1 = true
0:7/0 = 0
0:7/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
0:7/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
0:7/0/custom_data_1 = true
1:7/0 = 0
1:7/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
1:7/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
1:7/0/custom_data_1 = true
2:7/0 = 0
2:7/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
2:7/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
2:7/0/custom_data_1 = true
3:7/0 = 0
3:7/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
3:7/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
3:7/0/custom_data_1 = true
4:7/0 = 0
4:7/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
4:7/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
4:7/0/custom_data_1 = true
5:7/0 = 0
5:7/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
5:7/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
5:7/0/custom_data_1 = true
6:7/0 = 0
6:7/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
6:7/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
6:7/0/custom_data_1 = true
7:7/0 = 0
7:7/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
7:7/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
7:7/0/custom_data_1 = true
8:7/0 = 0
8:7/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
8:7/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
8:7/0/custom_data_1 = true
9:7/0 = 0
9:7/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
9:7/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
9:7/0/custom_data_1 = true
10:7/0 = 0
10:7/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
10:7/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
10:7/0/custom_data_1 = true
11:7/0 = 0
11:7/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
11:7/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
11:7/0/custom_data_1 = true
12:7/0 = 0
12:7/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
12:7/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
12:7/0/custom_data_1 = true
13:7/0 = 0
13:7/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
13:7/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
13:7/0/custom_data_1 = true
14:7/0 = 0
14:7/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
14:7/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
14:7/0/custom_data_1 = true
15:7/0 = 0
15:7/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
15:7/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
15:7/0/custom_data_1 = true
16:7/0 = 0
16:7/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
16:7/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
16:7/0/custom_data_1 = true
17:7/0 = 0
17:7/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
17:7/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
17:7/0/custom_data_1 = true
18:7/0 = 0
18:7/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
18:7/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
18:7/0/custom_data_1 = true
19:7/0 = 0
19:7/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
19:7/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
19:7/0/custom_data_1 = true
20:7/0 = 0
20:7/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
20:7/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
20:7/0/custom_data_1 = true
21:7/0 = 0
21:7/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
21:7/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
21:7/0/custom_data_1 = true
22:7/0 = 0
22:7/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
22:7/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
22:7/0/custom_data_1 = true
23:7/0 = 0
23:7/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
23:7/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
23:7/0/custom_data_1 = true
24:7/0 = 0
24:7/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
24:7/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
24:7/0/custom_data_1 = true
25:7/0 = 0
25:7/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
25:7/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
25:7/0/custom_data_1 = true
26:7/0 = 0
26:7/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
26:7/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
26:7/0/custom_data_1 = true
27:7/0 = 0
27:7/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
27:7/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
27:7/0/custom_data_1 = true
28:7/0 = 0
28:7/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
28:7/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
28:7/0/custom_data_1 = true
29:7/0 = 0
29:7/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
29:7/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
29:7/0/custom_data_1 = true
30:7/0 = 0
30:7/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
30:7/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
30:7/0/custom_data_1 = true
31:7/0 = 0
31:7/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
31:7/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
31:7/0/custom_data_1 = true
32:7/0 = 0
32:7/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
32:7/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
32:7/0/custom_data_1 = true
33:7/0 = 0
33:7/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
33:7/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
33:7/0/custom_data_1 = true
34:7/0 = 0
34:7/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
34:7/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
34:7/0/custom_data_1 = true
35:7/0 = 0
35:7/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
35:7/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
35:7/0/custom_data_1 = true
36:7/0 = 0
36:7/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
36:7/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
36:7/0/custom_data_1 = true
37:7/0 = 0
37:7/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
37:7/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
37:7/0/custom_data_1 = true
38:7/0 = 0
38:7/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
38:7/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
38:7/0/custom_data_1 = true
39:7/0 = 0
39:7/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
39:7/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
39:7/0/custom_data_1 = true
40:7/0 = 0
40:7/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
40:7/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
40:7/0/custom_data_1 = true
41:7/0 = 0
41:7/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
41:7/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
41:7/0/custom_data_1 = true
42:7/0 = 0
42:7/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
42:7/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
42:7/0/custom_data_1 = true
43:7/0 = 0
43:7/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
43:7/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
43:7/0/custom_data_1 = true
44:7/0 = 0
44:7/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
44:7/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
44:7/0/custom_data_1 = true
45:7/0 = 0
45:7/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
45:7/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
45:7/0/custom_data_1 = true
46:7/0 = 0
46:7/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
46:7/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
46:7/0/custom_data_1 = true
47:7/0 = 0
47:7/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
47:7/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
47:7/0/custom_data_1 = true
48:7/0 = 0
48:7/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
48:7/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
48:7/0/custom_data_1 = true
0:8/0 = 0
0:8/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
0:8/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
0:8/0/custom_data_1 = true
1:8/0 = 0
1:8/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
1:8/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
1:8/0/custom_data_1 = true
2:8/0 = 0
2:8/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
2:8/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
2:8/0/custom_data_1 = true
3:8/0 = 0
3:8/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
3:8/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
3:8/0/custom_data_1 = true
4:8/0 = 0
4:8/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
4:8/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
4:8/0/custom_data_1 = true
5:8/0 = 0
5:8/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
5:8/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
5:8/0/custom_data_1 = true
6:8/0 = 0
6:8/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
6:8/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
6:8/0/custom_data_1 = true
7:8/0 = 0
7:8/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
7:8/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
7:8/0/custom_data_1 = true
8:8/0 = 0
8:8/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
8:8/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
8:8/0/custom_data_1 = true
9:8/0 = 0
9:8/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
9:8/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
9:8/0/custom_data_1 = true
10:8/0 = 0
10:8/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
10:8/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
10:8/0/custom_data_1 = true
11:8/0 = 0
11:8/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
11:8/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
11:8/0/custom_data_1 = true
12:8/0 = 0
12:8/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
12:8/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
12:8/0/custom_data_1 = true
13:8/0 = 0
13:8/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
13:8/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
13:8/0/custom_data_1 = true
14:8/0 = 0
14:8/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
14:8/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
14:8/0/custom_data_1 = true
15:8/0 = 0
15:8/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
15:8/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
15:8/0/custom_data_1 = true
16:8/0 = 0
16:8/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
16:8/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
16:8/0/custom_data_1 = true
17:8/0 = 0
17:8/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
17:8/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
17:8/0/custom_data_1 = true
18:8/0 = 0
18:8/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
18:8/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
18:8/0/custom_data_1 = true
19:8/0 = 0
19:8/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
19:8/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
19:8/0/custom_data_1 = true
20:8/0 = 0
20:8/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
20:8/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
20:8/0/custom_data_1 = true
21:8/0 = 0
21:8/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
21:8/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
21:8/0/custom_data_1 = true
22:8/0 = 0
22:8/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
22:8/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
22:8/0/custom_data_1 = true
23:8/0 = 0
23:8/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
23:8/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
23:8/0/custom_data_1 = true
24:8/0 = 0
24:8/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
24:8/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
24:8/0/custom_data_1 = true
25:8/0 = 0
25:8/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
25:8/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
25:8/0/custom_data_1 = true
26:8/0 = 0
26:8/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
26:8/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
26:8/0/custom_data_1 = true
27:8/0 = 0
27:8/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
27:8/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
27:8/0/custom_data_1 = true
28:8/0 = 0
28:8/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
28:8/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
28:8/0/custom_data_1 = true
29:8/0 = 0
29:8/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
29:8/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
29:8/0/custom_data_1 = true
30:8/0 = 0
30:8/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
30:8/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
30:8/0/custom_data_1 = true
31:8/0 = 0
31:8/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
31:8/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
31:8/0/custom_data_1 = true
32:8/0 = 0
32:8/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
32:8/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
32:8/0/custom_data_1 = true
33:8/0 = 0
33:8/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
33:8/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
33:8/0/custom_data_1 = true
34:8/0 = 0
34:8/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
34:8/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
34:8/0/custom_data_1 = true
35:8/0 = 0
35:8/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
35:8/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
35:8/0/custom_data_1 = true
36:8/0 = 0
36:8/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
36:8/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
36:8/0/custom_data_1 = true
37:8/0 = 0
37:8/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
37:8/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
37:8/0/custom_data_1 = true
38:8/0 = 0
38:8/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
38:8/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
38:8/0/custom_data_1 = true
39:8/0 = 0
39:8/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
39:8/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
39:8/0/custom_data_1 = true
40:8/0 = 0
40:8/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
40:8/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
40:8/0/custom_data_1 = true
41:8/0 = 0
41:8/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
41:8/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
41:8/0/custom_data_1 = true
42:8/0 = 0
42:8/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
42:8/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
42:8/0/custom_data_1 = true
43:8/0 = 0
43:8/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
43:8/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
43:8/0/custom_data_1 = true
44:8/0 = 0
44:8/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
44:8/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
44:8/0/custom_data_1 = true
45:8/0 = 0
45:8/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
45:8/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
45:8/0/custom_data_1 = true
46:8/0 = 0
46:8/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
46:8/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
46:8/0/custom_data_1 = true
47:8/0 = 0
47:8/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
47:8/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
47:8/0/custom_data_1 = true
48:8/0 = 0
48:8/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
48:8/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
48:8/0/custom_data_1 = true
0:9/0 = 0
0:9/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
0:9/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
0:9/0/custom_data_1 = true
1:9/0 = 0
1:9/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
1:9/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
1:9/0/custom_data_1 = true
2:9/0 = 0
2:9/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
2:9/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
2:9/0/custom_data_1 = true
3:9/0 = 0
3:9/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
3:9/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
3:9/0/custom_data_1 = true
4:9/0 = 0
4:9/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
4:9/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
4:9/0/custom_data_1 = true
5:9/0 = 0
5:9/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
5:9/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
5:9/0/custom_data_1 = true
6:9/0 = 0
6:9/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
6:9/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
6:9/0/custom_data_1 = true
7:9/0 = 0
7:9/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
7:9/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
7:9/0/custom_data_1 = true
8:9/0 = 0
8:9/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
8:9/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
8:9/0/custom_data_1 = true
9:9/0 = 0
9:9/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
9:9/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
9:9/0/custom_data_1 = true
10:9/0 = 0
10:9/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
10:9/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
10:9/0/custom_data_1 = true
11:9/0 = 0
11:9/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
11:9/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
11:9/0/custom_data_1 = true
12:9/0 = 0
12:9/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
12:9/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
12:9/0/custom_data_1 = true
13:9/0 = 0
13:9/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
13:9/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
13:9/0/custom_data_1 = true
14:9/0 = 0
14:9/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
14:9/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
14:9/0/custom_data_1 = true
15:9/0 = 0
15:9/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
15:9/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
15:9/0/custom_data_1 = true
16:9/0 = 0
16:9/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
16:9/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
16:9/0/custom_data_1 = true
17:9/0 = 0
17:9/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
17:9/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
17:9/0/custom_data_1 = true
18:9/0 = 0
18:9/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
18:9/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
18:9/0/custom_data_1 = true
19:9/0 = 0
19:9/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
19:9/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
19:9/0/custom_data_1 = true
20:9/0 = 0
20:9/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
20:9/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
20:9/0/custom_data_1 = true
21:9/0 = 0
21:9/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
21:9/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
21:9/0/custom_data_1 = true
22:9/0 = 0
22:9/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
22:9/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
22:9/0/custom_data_1 = true
23:9/0 = 0
23:9/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
23:9/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
23:9/0/custom_data_1 = true
24:9/0 = 0
24:9/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
24:9/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
24:9/0/custom_data_1 = true
25:9/0 = 0
25:9/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
25:9/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
25:9/0/custom_data_1 = true
26:9/0 = 0
26:9/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
26:9/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
26:9/0/custom_data_1 = true
27:9/0 = 0
27:9/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
27:9/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
27:9/0/custom_data_1 = true
28:9/0 = 0
28:9/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
28:9/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
28:9/0/custom_data_1 = true
29:9/0 = 0
29:9/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
29:9/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
29:9/0/custom_data_1 = true
30:9/0 = 0
30:9/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
30:9/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
30:9/0/custom_data_1 = true
31:9/0 = 0
31:9/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
31:9/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
31:9/0/custom_data_1 = true
32:9/0 = 0
32:9/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
32:9/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
32:9/0/custom_data_1 = true
33:9/0 = 0
33:9/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
33:9/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
33:9/0/custom_data_1 = true
34:9/0 = 0
34:9/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
34:9/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
34:9/0/custom_data_1 = true
35:9/0 = 0
35:9/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
35:9/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
35:9/0/custom_data_1 = true
36:9/0 = 0
36:9/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
36:9/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
36:9/0/custom_data_1 = true
37:9/0 = 0
37:9/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
37:9/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
37:9/0/custom_data_1 = true
38:9/0 = 0
38:9/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
38:9/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
38:9/0/custom_data_1 = true
39:9/0 = 0
39:9/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
39:9/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
39:9/0/custom_data_1 = true
40:9/0 = 0
40:9/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
40:9/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
40:9/0/custom_data_1 = true
41:9/0 = 0
41:9/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
41:9/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
41:9/0/custom_data_1 = true
42:9/0 = 0
42:9/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
42:9/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
42:9/0/custom_data_1 = true
43:9/0 = 0
43:9/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
43:9/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
43:9/0/custom_data_1 = true
44:9/0 = 0
44:9/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
44:9/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
44:9/0/custom_data_1 = true
45:9/0 = 0
45:9/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
45:9/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
45:9/0/custom_data_1 = true
46:9/0 = 0
46:9/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
46:9/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
46:9/0/custom_data_1 = true
47:9/0 = 0
47:9/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
47:9/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
47:9/0/custom_data_1 = true
48:9/0 = 0
48:9/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
48:9/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
48:9/0/custom_data_1 = true
0:10/0 = 0
0:10/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
0:10/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
0:10/0/custom_data_1 = true
1:10/0 = 0
1:10/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
1:10/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
1:10/0/custom_data_1 = true
2:10/0 = 0
2:10/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
2:10/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
2:10/0/custom_data_1 = true
3:10/0 = 0
3:10/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
3:10/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
3:10/0/custom_data_1 = true
4:10/0 = 0
4:10/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
4:10/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
4:10/0/custom_data_1 = true
5:10/0 = 0
5:10/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
5:10/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
5:10/0/custom_data_1 = true
6:10/0 = 0
6:10/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
6:10/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
6:10/0/custom_data_1 = true
7:10/0 = 0
7:10/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
7:10/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
7:10/0/custom_data_1 = true
8:10/0 = 0
8:10/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
8:10/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
8:10/0/custom_data_1 = true
9:10/0 = 0
9:10/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
9:10/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
9:10/0/custom_data_1 = true
10:10/0 = 0
10:10/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
10:10/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
10:10/0/custom_data_1 = true
11:10/0 = 0
11:10/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
11:10/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
11:10/0/custom_data_1 = true
12:10/0 = 0
12:10/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
12:10/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
12:10/0/custom_data_1 = true
13:10/0 = 0
13:10/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
13:10/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
13:10/0/custom_data_1 = true
14:10/0 = 0
14:10/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
14:10/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
14:10/0/custom_data_1 = true
15:10/0 = 0
15:10/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
15:10/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
15:10/0/custom_data_1 = true
16:10/0 = 0
16:10/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
16:10/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
16:10/0/custom_data_1 = true
17:10/0 = 0
17:10/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
17:10/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
17:10/0/custom_data_1 = true
18:10/0 = 0
18:10/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
18:10/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
18:10/0/custom_data_1 = true
19:10/0 = 0
19:10/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
19:10/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
19:10/0/custom_data_1 = true
20:10/0 = 0
20:10/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
20:10/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
20:10/0/custom_data_1 = true
21:10/0 = 0
21:10/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
21:10/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
21:10/0/custom_data_1 = true
22:10/0 = 0
22:10/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
22:10/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
22:10/0/custom_data_1 = true
23:10/0 = 0
23:10/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
23:10/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
23:10/0/custom_data_1 = true
24:10/0 = 0
24:10/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
24:10/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
24:10/0/custom_data_1 = true
25:10/0 = 0
25:10/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
25:10/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
25:10/0/custom_data_1 = true
26:10/0 = 0
26:10/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
26:10/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
26:10/0/custom_data_1 = true
27:10/0 = 0
27:10/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
27:10/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
27:10/0/custom_data_1 = true
28:10/0 = 0
28:10/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
28:10/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
28:10/0/custom_data_1 = true
29:10/0 = 0
29:10/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
29:10/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
29:10/0/custom_data_1 = true
30:10/0 = 0
30:10/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
30:10/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
30:10/0/custom_data_1 = true
31:10/0 = 0
31:10/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
31:10/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
31:10/0/custom_data_1 = true
32:10/0 = 0
32:10/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
32:10/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
32:10/0/custom_data_1 = true
33:10/0 = 0
33:10/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
33:10/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
33:10/0/custom_data_1 = true
34:10/0 = 0
34:10/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
34:10/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
34:10/0/custom_data_1 = true
35:10/0 = 0
35:10/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
35:10/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
35:10/0/custom_data_1 = true
36:10/0 = 0
36:10/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
36:10/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
36:10/0/custom_data_1 = true
37:10/0 = 0
37:10/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
37:10/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
37:10/0/custom_data_1 = true
38:10/0 = 0
38:10/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
38:10/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
38:10/0/custom_data_1 = true
39:10/0 = 0
39:10/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
39:10/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
39:10/0/custom_data_1 = true
40:10/0 = 0
40:10/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
40:10/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
40:10/0/custom_data_1 = true
41:10/0 = 0
41:10/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
41:10/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
41:10/0/custom_data_1 = true
42:10/0 = 0
42:10/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
42:10/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
42:10/0/custom_data_1 = true
43:10/0 = 0
43:10/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
43:10/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
43:10/0/custom_data_1 = true
44:10/0 = 0
44:10/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
44:10/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
44:10/0/custom_data_1 = true
45:10/0 = 0
45:10/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
45:10/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
45:10/0/custom_data_1 = true
46:10/0 = 0
46:10/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
46:10/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
46:10/0/custom_data_1 = true
47:10/0 = 0
47:10/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
47:10/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
47:10/0/custom_data_1 = true
48:10/0 = 0
48:10/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
48:10/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
48:10/0/custom_data_1 = true
0:11/0 = 0
0:11/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
0:11/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
0:11/0/custom_data_1 = true
1:11/0 = 0
1:11/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_dq1fv")
1:11/0/physics_layer_0/polygon_0/points = PackedVector2Array(8, -8, 8, 8, -8, 8)
1:11/0/custom_data_1 = true
2:11/0 = 0
2:11/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
2:11/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
2:11/0/custom_data_1 = true
3:11/0 = 0
3:11/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
3:11/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
3:11/0/custom_data_1 = true
4:11/0 = 0
4:11/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
4:11/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
4:11/0/custom_data_1 = true
5:11/0 = 0
5:11/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
5:11/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
5:11/0/custom_data_1 = true
6:11/0 = 0
6:11/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
6:11/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
6:11/0/custom_data_1 = true
7:11/0 = 0
7:11/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
7:11/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
7:11/0/custom_data_1 = true
8:11/0 = 0
8:11/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
8:11/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
8:11/0/custom_data_1 = true
9:11/0 = 0
9:11/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
9:11/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
9:11/0/custom_data_1 = true
10:11/0 = 0
10:11/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
10:11/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
10:11/0/custom_data_1 = true
11:11/0 = 0
11:11/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
11:11/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
11:11/0/custom_data_1 = true
12:11/0 = 0
12:11/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
12:11/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
12:11/0/custom_data_1 = true
13:11/0 = 0
13:11/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
13:11/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
13:11/0/custom_data_1 = true
14:11/0 = 0
14:11/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
14:11/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
14:11/0/custom_data_1 = true
15:11/0 = 0
15:11/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
15:11/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
15:11/0/custom_data_1 = true
16:11/0 = 0
16:11/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
16:11/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
16:11/0/custom_data_1 = true
17:11/0 = 0
17:11/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
17:11/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
17:11/0/custom_data_1 = true
18:11/0 = 0
18:11/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
18:11/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
18:11/0/custom_data_1 = true
19:11/0 = 0
19:11/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
19:11/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
19:11/0/custom_data_1 = true
20:11/0 = 0
20:11/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
20:11/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
20:11/0/custom_data_1 = true
21:11/0 = 0
21:11/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
21:11/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
21:11/0/custom_data_1 = true
22:11/0 = 0
22:11/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
22:11/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
22:11/0/custom_data_1 = true
23:11/0 = 0
23:11/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
23:11/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
23:11/0/custom_data_1 = true
24:11/0 = 0
24:11/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
24:11/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
24:11/0/custom_data_1 = true
25:11/0 = 0
25:11/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
25:11/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
25:11/0/custom_data_1 = true
26:11/0 = 0
26:11/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
26:11/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
26:11/0/custom_data_1 = true
27:11/0 = 0
27:11/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
27:11/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
27:11/0/custom_data_1 = true
28:11/0 = 0
28:11/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
28:11/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
28:11/0/custom_data_1 = true
29:11/0 = 0
29:11/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
29:11/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
29:11/0/custom_data_1 = true
30:11/0 = 0
30:11/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
30:11/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
30:11/0/custom_data_1 = true
31:11/0 = 0
31:11/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
31:11/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
31:11/0/custom_data_1 = true
32:11/0 = 0
32:11/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
32:11/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
32:11/0/custom_data_1 = true
33:11/0 = 0
33:11/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
33:11/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
33:11/0/custom_data_1 = true
34:11/0 = 0
34:11/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
34:11/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
34:11/0/custom_data_1 = true
35:11/0 = 0
35:11/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
35:11/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
35:11/0/custom_data_1 = true
36:11/0 = 0
36:11/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
36:11/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
36:11/0/custom_data_1 = true
37:11/0 = 0
37:11/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
37:11/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
37:11/0/custom_data_1 = true
38:11/0 = 0
38:11/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
38:11/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
38:11/0/custom_data_1 = true
39:11/0 = 0
39:11/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
39:11/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
39:11/0/custom_data_1 = true
40:11/0 = 0
40:11/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
40:11/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
40:11/0/custom_data_1 = true
41:11/0 = 0
41:11/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
41:11/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
41:11/0/custom_data_1 = true
42:11/0 = 0
42:11/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
42:11/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
42:11/0/custom_data_1 = true
43:11/0 = 0
43:11/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
43:11/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
43:11/0/custom_data_1 = true
44:11/0 = 0
44:11/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
44:11/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
44:11/0/custom_data_1 = true
45:11/0 = 0
45:11/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
45:11/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
45:11/0/custom_data_1 = true
46:11/0 = 0
46:11/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
46:11/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
46:11/0/custom_data_1 = true
47:11/0 = 0
47:11/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
47:11/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
47:11/0/custom_data_1 = true
48:11/0 = 0
48:11/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
48:11/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
48:11/0/custom_data_1 = true
0:12/0 = 0
0:12/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
0:12/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
0:12/0/custom_data_1 = true
1:12/0 = 0
1:12/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
1:12/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
1:12/0/custom_data_1 = true
2:12/0 = 0
2:12/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
2:12/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
2:12/0/custom_data_1 = true
3:12/0 = 0
3:12/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
3:12/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
3:12/0/custom_data_1 = true
4:12/0 = 0
4:12/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
4:12/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
4:12/0/custom_data_1 = true
5:12/0 = 0
5:12/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
5:12/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
5:12/0/custom_data_1 = true
6:12/0 = 0
6:12/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
6:12/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
6:12/0/custom_data_1 = true
7:12/0 = 0
7:12/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
7:12/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
7:12/0/custom_data_1 = true
8:12/0 = 0
8:12/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
8:12/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
8:12/0/custom_data_1 = true
9:12/0 = 0
9:12/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
9:12/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
9:12/0/custom_data_1 = true
10:12/0 = 0
10:12/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
10:12/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
10:12/0/custom_data_1 = true
11:12/0 = 0
11:12/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
11:12/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
11:12/0/custom_data_1 = true
12:12/0 = 0
12:12/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
12:12/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
12:12/0/custom_data_1 = true
13:12/0 = 0
13:12/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
13:12/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
13:12/0/custom_data_1 = true
14:12/0 = 0
14:12/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
14:12/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
14:12/0/custom_data_1 = true
15:12/0 = 0
15:12/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
15:12/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
15:12/0/custom_data_1 = true
16:12/0 = 0
16:12/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
16:12/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
16:12/0/custom_data_1 = true
17:12/0 = 0
17:12/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
17:12/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
17:12/0/custom_data_1 = true
18:12/0 = 0
18:12/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
18:12/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
18:12/0/custom_data_1 = true
19:12/0 = 0
19:12/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
19:12/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
19:12/0/custom_data_1 = true
20:12/0 = 0
20:12/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
20:12/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
20:12/0/custom_data_1 = true
21:12/0 = 0
21:12/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
21:12/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
21:12/0/custom_data_1 = true
22:12/0 = 0
22:12/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
22:12/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
22:12/0/custom_data_1 = true
23:12/0 = 0
23:12/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
23:12/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
23:12/0/custom_data_1 = true
24:12/0 = 0
24:12/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
24:12/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
24:12/0/custom_data_1 = true
25:12/0 = 0
25:12/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
25:12/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
25:12/0/custom_data_1 = true
26:12/0 = 0
26:12/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
26:12/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
26:12/0/custom_data_1 = true
27:12/0 = 0
27:12/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
27:12/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
27:12/0/custom_data_1 = true
28:12/0 = 0
28:12/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
28:12/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
28:12/0/custom_data_1 = true
29:12/0 = 0
29:12/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
29:12/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
29:12/0/custom_data_1 = true
30:12/0 = 0
30:12/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
30:12/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
30:12/0/custom_data_1 = true
31:12/0 = 0
31:12/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
31:12/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
31:12/0/custom_data_1 = true
32:12/0 = 0
32:12/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
32:12/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
32:12/0/custom_data_1 = true
33:12/0 = 0
33:12/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
33:12/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
33:12/0/custom_data_1 = true
34:12/0 = 0
34:12/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
34:12/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
34:12/0/custom_data_1 = true
35:12/0 = 0
35:12/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
35:12/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
35:12/0/custom_data_1 = true
36:12/0 = 0
36:12/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
36:12/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
36:12/0/custom_data_1 = true
37:12/0 = 0
37:12/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
37:12/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
37:12/0/custom_data_1 = true
38:12/0 = 0
38:12/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
38:12/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
38:12/0/custom_data_1 = true
39:12/0 = 0
39:12/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
39:12/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
39:12/0/custom_data_1 = true
40:12/0 = 0
40:12/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
40:12/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
40:12/0/custom_data_1 = true
41:12/0 = 0
41:12/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
41:12/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
41:12/0/custom_data_1 = true
42:12/0 = 0
42:12/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
42:12/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
42:12/0/custom_data_1 = true
43:12/0 = 0
43:12/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
43:12/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
43:12/0/custom_data_1 = true
44:12/0 = 0
44:12/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
44:12/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
44:12/0/custom_data_1 = true
45:12/0 = 0
45:12/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
45:12/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
45:12/0/custom_data_1 = true
46:12/0 = 0
46:12/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
46:12/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
46:12/0/custom_data_1 = true
47:12/0 = 0
47:12/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
47:12/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
47:12/0/custom_data_1 = true
48:12/0 = 0
48:12/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
48:12/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
48:12/0/custom_data_1 = true
0:13/0 = 0
0:13/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
0:13/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
0:13/0/custom_data_1 = true
1:13/0 = 0
1:13/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
1:13/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
1:13/0/custom_data_1 = true
2:13/0 = 0
2:13/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
2:13/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
2:13/0/custom_data_1 = true
3:13/0 = 0
3:13/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
3:13/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
3:13/0/custom_data_1 = true
4:13/0 = 0
4:13/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
4:13/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
4:13/0/custom_data_1 = true
5:13/0 = 0
5:13/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
5:13/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
5:13/0/custom_data_1 = true
6:13/0 = 0
6:13/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
6:13/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
6:13/0/custom_data_1 = true
7:13/0 = 0
7:13/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
7:13/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
7:13/0/custom_data_1 = true
8:13/0 = 0
8:13/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
8:13/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
8:13/0/custom_data_1 = true
9:13/0 = 0
9:13/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
9:13/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
9:13/0/custom_data_1 = true
10:13/0 = 0
10:13/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
10:13/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
10:13/0/custom_data_1 = true
11:13/0 = 0
11:13/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
11:13/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
11:13/0/custom_data_1 = true
12:13/0 = 0
12:13/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
12:13/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
12:13/0/custom_data_1 = true
13:13/0 = 0
13:13/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
13:13/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
13:13/0/custom_data_1 = true
14:13/0 = 0
14:13/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
14:13/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
14:13/0/custom_data_1 = true
15:13/0 = 0
15:13/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
15:13/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
15:13/0/custom_data_1 = true
16:13/0 = 0
16:13/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
16:13/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
16:13/0/custom_data_1 = true
17:13/0 = 0
17:13/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
17:13/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
17:13/0/custom_data_1 = true
18:13/0 = 0
18:13/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
18:13/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
18:13/0/custom_data_1 = true
19:13/0 = 0
19:13/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
19:13/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
19:13/0/custom_data_1 = true
20:13/0 = 0
20:13/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
20:13/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
20:13/0/custom_data_1 = true
21:13/0 = 0
21:13/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
21:13/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
21:13/0/custom_data_1 = true
22:13/0 = 0
22:13/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
22:13/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
22:13/0/custom_data_1 = true
23:13/0 = 0
23:13/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
23:13/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
23:13/0/custom_data_1 = true
24:13/0 = 0
24:13/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
24:13/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
24:13/0/custom_data_1 = true
25:13/0 = 0
25:13/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
25:13/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
25:13/0/custom_data_1 = true
26:13/0 = 0
26:13/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
26:13/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
26:13/0/custom_data_1 = true
27:13/0 = 0
27:13/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
27:13/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
27:13/0/custom_data_1 = true
28:13/0 = 0
28:13/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
28:13/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
28:13/0/custom_data_1 = true
29:13/0 = 0
29:13/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
29:13/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
29:13/0/custom_data_1 = true
30:13/0 = 0
30:13/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
30:13/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
30:13/0/custom_data_1 = true
31:13/0 = 0
31:13/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
31:13/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
31:13/0/custom_data_1 = true
32:13/0 = 0
32:13/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
32:13/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
32:13/0/custom_data_1 = true
33:13/0 = 0
33:13/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
33:13/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
33:13/0/custom_data_1 = true
34:13/0 = 0
34:13/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
34:13/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
34:13/0/custom_data_1 = true
35:13/0 = 0
35:13/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
35:13/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
35:13/0/custom_data_1 = true
36:13/0 = 0
36:13/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
36:13/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
36:13/0/custom_data_1 = true
37:13/0 = 0
37:13/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
37:13/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
37:13/0/custom_data_1 = true
38:13/0 = 0
38:13/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
38:13/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
38:13/0/custom_data_1 = true
39:13/0 = 0
39:13/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
39:13/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
39:13/0/custom_data_1 = true
40:13/0 = 0
40:13/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
40:13/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
40:13/0/custom_data_1 = true
41:13/0 = 0
41:13/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
41:13/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
41:13/0/custom_data_1 = true
42:13/0 = 0
42:13/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
42:13/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
42:13/0/custom_data_1 = true
43:13/0 = 0
43:13/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
43:13/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
43:13/0/custom_data_1 = true
44:13/0 = 0
44:13/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
44:13/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
44:13/0/custom_data_1 = true
45:13/0 = 0
45:13/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
45:13/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
45:13/0/custom_data_1 = true
46:13/0 = 0
46:13/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
46:13/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
46:13/0/custom_data_1 = true
47:13/0 = 0
47:13/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
47:13/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
47:13/0/custom_data_1 = true
48:13/0 = 0
48:13/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
48:13/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
48:13/0/custom_data_1 = true
0:14/0 = 0
0:14/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
0:14/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
0:14/0/custom_data_1 = true
1:14/0 = 0
1:14/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
1:14/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
1:14/0/custom_data_1 = true
2:14/0 = 0
2:14/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
2:14/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
2:14/0/custom_data_1 = true
3:14/0 = 0
3:14/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
3:14/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
3:14/0/custom_data_1 = true
4:14/0 = 0
4:14/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
4:14/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
4:14/0/custom_data_1 = true
5:14/0 = 0
5:14/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
5:14/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
5:14/0/custom_data_1 = true
6:14/0 = 0
6:14/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
6:14/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
6:14/0/custom_data_1 = true
7:14/0 = 0
7:14/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
7:14/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
7:14/0/custom_data_1 = true
8:14/0 = 0
8:14/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
8:14/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
8:14/0/custom_data_1 = true
9:14/0 = 0
9:14/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
9:14/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
9:14/0/custom_data_1 = true
10:14/0 = 0
10:14/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
10:14/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
10:14/0/custom_data_1 = true
11:14/0 = 0
11:14/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
11:14/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
11:14/0/custom_data_1 = true
12:14/0 = 0
12:14/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
12:14/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
12:14/0/custom_data_1 = true
13:14/0 = 0
13:14/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
13:14/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
13:14/0/custom_data_1 = true
14:14/0 = 0
14:14/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
14:14/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
14:14/0/custom_data_1 = true
15:14/0 = 0
15:14/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
15:14/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
15:14/0/custom_data_1 = true
16:14/0 = 0
16:14/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
16:14/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
16:14/0/custom_data_1 = true
17:14/0 = 0
17:14/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
17:14/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
17:14/0/custom_data_1 = true
18:14/0 = 0
18:14/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
18:14/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
18:14/0/custom_data_1 = true
19:14/0 = 0
19:14/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
19:14/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
19:14/0/custom_data_1 = true
20:14/0 = 0
20:14/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
20:14/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
20:14/0/custom_data_1 = true
21:14/0 = 0
21:14/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
21:14/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
21:14/0/custom_data_1 = true
22:14/0 = 0
22:14/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
22:14/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
22:14/0/custom_data_1 = true
23:14/0 = 0
23:14/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
23:14/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
23:14/0/custom_data_1 = true
24:14/0 = 0
24:14/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
24:14/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
24:14/0/custom_data_1 = true
25:14/0 = 0
25:14/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
25:14/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
25:14/0/custom_data_1 = true
26:14/0 = 0
26:14/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
26:14/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
26:14/0/custom_data_1 = true
27:14/0 = 0
27:14/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
27:14/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
27:14/0/custom_data_1 = true
28:14/0 = 0
28:14/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
28:14/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
28:14/0/custom_data_1 = true
29:14/0 = 0
29:14/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
29:14/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
29:14/0/custom_data_1 = true
30:14/0 = 0
30:14/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
30:14/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
30:14/0/custom_data_1 = true
31:14/0 = 0
31:14/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
31:14/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
31:14/0/custom_data_1 = true
32:14/0 = 0
32:14/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
32:14/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
32:14/0/custom_data_1 = true
33:14/0 = 0
33:14/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
33:14/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
33:14/0/custom_data_1 = true
34:14/0 = 0
34:14/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
34:14/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
34:14/0/custom_data_1 = true
35:14/0 = 0
35:14/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
35:14/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
35:14/0/custom_data_1 = true
36:14/0 = 0
36:14/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
36:14/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
36:14/0/custom_data_1 = true
37:14/0 = 0
37:14/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
37:14/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
37:14/0/custom_data_1 = true
38:14/0 = 0
38:14/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
38:14/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
38:14/0/custom_data_1 = true
39:14/0 = 0
39:14/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
39:14/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
39:14/0/custom_data_1 = true
40:14/0 = 0
40:14/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
40:14/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
40:14/0/custom_data_1 = true
41:14/0 = 0
41:14/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
41:14/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
41:14/0/custom_data_1 = true
42:14/0 = 0
42:14/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
42:14/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
42:14/0/custom_data_1 = true
43:14/0 = 0
43:14/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
43:14/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
43:14/0/custom_data_1 = true
44:14/0 = 0
44:14/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
44:14/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
44:14/0/custom_data_1 = true
45:14/0 = 0
45:14/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
45:14/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
45:14/0/custom_data_1 = true
46:14/0 = 0
46:14/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
46:14/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
46:14/0/custom_data_1 = true
47:14/0 = 0
47:14/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
47:14/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
47:14/0/custom_data_1 = true
48:14/0 = 0
48:14/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
48:14/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
48:14/0/custom_data_1 = true
0:15/0 = 0
0:15/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
0:15/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
0:15/0/custom_data_1 = true
1:15/0 = 0
1:15/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
1:15/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
1:15/0/custom_data_1 = true
2:15/0 = 0
2:15/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
2:15/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
2:15/0/custom_data_1 = true
3:15/0 = 0
3:15/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
3:15/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
3:15/0/custom_data_1 = true
4:15/0 = 0
4:15/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
4:15/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
4:15/0/custom_data_1 = true
5:15/0 = 0
5:15/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
5:15/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
5:15/0/custom_data_1 = true
6:15/0 = 0
6:15/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
6:15/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
6:15/0/custom_data_1 = true
7:15/0 = 0
7:15/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
7:15/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
7:15/0/custom_data_1 = true
8:15/0 = 0
8:15/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
8:15/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
8:15/0/custom_data_1 = true
9:15/0 = 0
9:15/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
9:15/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
9:15/0/custom_data_1 = true
10:15/0 = 0
10:15/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
10:15/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
10:15/0/custom_data_1 = true
11:15/0 = 0
11:15/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
11:15/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
11:15/0/custom_data_1 = true
12:15/0 = 0
12:15/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
12:15/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
12:15/0/custom_data_1 = true
13:15/0 = 0
13:15/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
13:15/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
13:15/0/custom_data_1 = true
14:15/0 = 0
14:15/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
14:15/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
14:15/0/custom_data_1 = true
15:15/0 = 0
15:15/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
15:15/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
15:15/0/custom_data_1 = true
16:15/0 = 0
16:15/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
16:15/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
16:15/0/custom_data_1 = true
17:15/0 = 0
17:15/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
17:15/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
17:15/0/custom_data_1 = true
18:15/0 = 0
18:15/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
18:15/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
18:15/0/custom_data_1 = true
19:15/0 = 0
19:15/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_udp1x")
19:15/0/physics_layer_0/polygon_0/points = PackedVector2Array(-6, -4, 8, -4, 8, 2, -6, 2)
19:15/0/physics_layer_0/polygon_0/one_way = true
19:15/0/custom_data_0 = true
20:15/0 = 0
20:15/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_0b2md")
20:15/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -4, 8, -4, 8, 2, -8, 2)
20:15/0/physics_layer_0/polygon_0/one_way = true
20:15/0/custom_data_0 = true
21:15/0 = 0
21:15/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
21:15/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
21:15/0/custom_data_0 = true
22:15/0 = 0
22:15/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
22:15/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
22:15/0/custom_data_0 = true
23:15/0 = 0
23:15/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
23:15/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -4, 8, -4, 8, 4, -8, 4)
23:15/0/physics_layer_0/polygon_0/one_way = true
23:15/0/custom_data_0 = true
24:15/0 = 0
24:15/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
24:15/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -4, 8, -4, 8, 4, -8, 4)
24:15/0/physics_layer_0/polygon_0/one_way = true
24:15/0/custom_data_0 = true
25:15/0 = 0
25:15/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
25:15/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
25:15/0/custom_data_1 = true
26:15/0 = 0
26:15/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
26:15/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
26:15/0/custom_data_1 = true
27:15/0 = 0
27:15/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
27:15/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
27:15/0/custom_data_1 = true
28:15/0 = 0
28:15/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
28:15/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
28:15/0/custom_data_1 = true
29:15/0 = 0
29:15/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
29:15/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
29:15/0/custom_data_1 = true
30:15/0 = 0
30:15/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
30:15/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
30:15/0/custom_data_1 = true
31:15/0 = 0
31:15/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
31:15/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
31:15/0/custom_data_1 = true
32:15/0 = 0
32:15/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
32:15/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
32:15/0/custom_data_1 = true
33:15/0 = 0
33:15/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
33:15/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
33:15/0/custom_data_1 = true
34:15/0 = 0
34:15/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
34:15/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
34:15/0/custom_data_1 = true
35:15/0 = 0
35:15/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
35:15/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
35:15/0/custom_data_1 = true
36:15/0 = 0
36:15/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
36:15/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
36:15/0/custom_data_1 = true
37:15/0 = 0
37:15/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
37:15/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
37:15/0/custom_data_1 = true
38:15/0 = 0
38:15/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
38:15/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
38:15/0/custom_data_1 = true
39:15/0 = 0
39:15/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
39:15/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
39:15/0/custom_data_1 = true
40:15/0 = 0
40:15/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
40:15/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
40:15/0/custom_data_1 = true
41:15/0 = 0
41:15/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
41:15/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
41:15/0/custom_data_1 = true
42:15/0 = 0
42:15/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
42:15/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
42:15/0/custom_data_1 = true
43:15/0 = 0
43:15/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
43:15/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
43:15/0/custom_data_1 = true
44:15/0 = 0
44:15/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
44:15/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
44:15/0/custom_data_1 = true
45:15/0 = 0
45:15/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
45:15/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
45:15/0/custom_data_1 = true
46:15/0 = 0
46:15/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
46:15/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
46:15/0/custom_data_1 = true
47:15/0 = 0
47:15/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
47:15/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
47:15/0/custom_data_1 = true
48:15/0 = 0
48:15/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
48:15/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
48:15/0/custom_data_1 = true
0:16/0 = 0
0:16/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
0:16/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
0:16/0/custom_data_1 = true
1:16/0 = 0
1:16/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
1:16/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
1:16/0/custom_data_1 = true
2:16/0 = 0
2:16/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
2:16/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
2:16/0/custom_data_1 = true
3:16/0 = 0
3:16/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
3:16/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
3:16/0/custom_data_1 = true
4:16/0 = 0
4:16/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
4:16/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
4:16/0/custom_data_1 = true
5:16/0 = 0
5:16/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
5:16/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
5:16/0/custom_data_1 = true
6:16/0 = 0
6:16/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
6:16/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
6:16/0/custom_data_1 = true
7:16/0 = 0
7:16/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
7:16/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
7:16/0/custom_data_1 = true
8:16/0 = 0
8:16/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
8:16/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
8:16/0/custom_data_1 = true
9:16/0 = 0
9:16/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
9:16/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
9:16/0/custom_data_1 = true
10:16/0 = 0
10:16/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
10:16/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
10:16/0/custom_data_1 = true
11:16/0 = 0
11:16/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
11:16/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
11:16/0/custom_data_1 = true
12:16/0 = 0
12:16/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
12:16/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
12:16/0/custom_data_1 = true
13:16/0 = 0
13:16/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
13:16/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
13:16/0/custom_data_1 = true
14:16/0 = 0
14:16/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
14:16/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
14:16/0/custom_data_1 = true
15:16/0 = 0
15:16/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
15:16/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
15:16/0/custom_data_1 = true
16:16/0 = 0
16:16/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
16:16/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
16:16/0/custom_data_1 = true
17:16/0 = 0
17:16/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
17:16/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
17:16/0/custom_data_1 = true
18:16/0 = 0
18:16/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
18:16/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
18:16/0/custom_data_1 = true
19:16/0 = 0
19:16/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
19:16/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
19:16/0/custom_data_1 = true
20:16/0 = 0
20:16/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
20:16/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
20:16/0/custom_data_1 = true
21:16/0 = 0
21:16/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
21:16/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
21:16/0/custom_data_1 = true
22:16/0 = 0
22:16/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
22:16/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
22:16/0/custom_data_1 = true
23:16/0 = 0
23:16/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
23:16/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
23:16/0/custom_data_1 = true
24:16/0 = 0
24:16/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
24:16/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
24:16/0/custom_data_1 = true
25:16/0 = 0
25:16/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
25:16/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
25:16/0/custom_data_1 = true
26:16/0 = 0
26:16/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
26:16/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
26:16/0/custom_data_1 = true
27:16/0 = 0
27:16/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
27:16/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
27:16/0/custom_data_1 = true
28:16/0 = 0
28:16/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
28:16/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
28:16/0/custom_data_1 = true
29:16/0 = 0
29:16/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
29:16/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
29:16/0/custom_data_1 = true
30:16/0 = 0
30:16/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
30:16/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
30:16/0/custom_data_1 = true
31:16/0 = 0
31:16/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
31:16/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
31:16/0/custom_data_1 = true
32:16/0 = 0
32:16/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
32:16/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
32:16/0/custom_data_1 = true
33:16/0 = 0
33:16/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
33:16/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
33:16/0/custom_data_1 = true
34:16/0 = 0
34:16/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
34:16/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
34:16/0/custom_data_1 = true
35:16/0 = 0
35:16/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
35:16/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
35:16/0/custom_data_1 = true
36:16/0 = 0
36:16/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
36:16/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
36:16/0/custom_data_1 = true
37:16/0 = 0
37:16/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
37:16/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
37:16/0/custom_data_1 = true
38:16/0 = 0
38:16/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
38:16/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
38:16/0/custom_data_1 = true
39:16/0 = 0
39:16/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
39:16/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
39:16/0/custom_data_1 = true
40:16/0 = 0
40:16/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
40:16/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
40:16/0/custom_data_1 = true
41:16/0 = 0
41:16/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
41:16/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
41:16/0/custom_data_1 = true
42:16/0 = 0
42:16/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
42:16/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
42:16/0/custom_data_1 = true
43:16/0 = 0
43:16/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
43:16/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
43:16/0/custom_data_1 = true
44:16/0 = 0
44:16/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
44:16/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
44:16/0/custom_data_1 = true
45:16/0 = 0
45:16/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
45:16/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
45:16/0/custom_data_1 = true
46:16/0 = 0
46:16/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
46:16/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
46:16/0/custom_data_1 = true
47:16/0 = 0
47:16/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
47:16/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
47:16/0/custom_data_1 = true
48:16/0 = 0
48:16/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
48:16/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
48:16/0/custom_data_1 = true
0:17/0 = 0
0:17/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
0:17/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
0:17/0/custom_data_1 = true
1:17/0 = 0
1:17/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
1:17/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
1:17/0/custom_data_1 = true
2:17/0 = 0
2:17/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
2:17/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
2:17/0/custom_data_1 = true
3:17/0 = 0
3:17/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
3:17/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
3:17/0/custom_data_1 = true
4:17/0 = 0
4:17/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
4:17/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
4:17/0/custom_data_1 = true
5:17/0 = 0
5:17/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
5:17/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
5:17/0/custom_data_1 = true
6:17/0 = 0
6:17/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
6:17/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
6:17/0/custom_data_1 = true
7:17/0 = 0
7:17/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
7:17/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
7:17/0/custom_data_1 = true
8:17/0 = 0
8:17/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
8:17/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
8:17/0/custom_data_1 = true
9:17/0 = 0
9:17/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
9:17/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
9:17/0/custom_data_1 = true
10:17/0 = 0
10:17/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
10:17/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
10:17/0/custom_data_1 = true
11:17/0 = 0
11:17/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
11:17/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
11:17/0/custom_data_1 = true
12:17/0 = 0
12:17/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
12:17/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
12:17/0/custom_data_1 = true
13:17/0 = 0
13:17/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
13:17/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
13:17/0/custom_data_1 = true
14:17/0 = 0
14:17/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
14:17/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
14:17/0/custom_data_1 = true
15:17/0 = 0
15:17/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
15:17/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
15:17/0/custom_data_1 = true
16:17/0 = 0
16:17/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
16:17/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
16:17/0/custom_data_1 = true
17:17/0 = 0
17:17/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
17:17/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
17:17/0/custom_data_1 = true
18:17/0 = 0
18:17/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
18:17/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
18:17/0/custom_data_1 = true
19:17/0 = 0
19:17/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
19:17/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
19:17/0/custom_data_1 = true
20:17/0 = 0
20:17/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
20:17/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
20:17/0/custom_data_1 = true
21:17/0 = 0
21:17/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
21:17/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
21:17/0/custom_data_1 = true
22:17/0 = 0
22:17/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
22:17/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
22:17/0/custom_data_1 = true
23:17/0 = 0
23:17/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
23:17/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
23:17/0/custom_data_1 = true
24:17/0 = 0
24:17/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
24:17/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
24:17/0/custom_data_1 = true
25:17/0 = 0
25:17/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
25:17/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
25:17/0/custom_data_1 = true
26:17/0 = 0
26:17/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
26:17/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
26:17/0/custom_data_1 = true
27:17/0 = 0
27:17/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
27:17/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
27:17/0/custom_data_1 = true
28:17/0 = 0
28:17/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
28:17/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
28:17/0/custom_data_1 = true
29:17/0 = 0
29:17/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
29:17/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
29:17/0/custom_data_1 = true
30:17/0 = 0
30:17/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
30:17/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
30:17/0/custom_data_1 = true
31:17/0 = 0
31:17/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
31:17/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
31:17/0/custom_data_1 = true
32:17/0 = 0
32:17/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
32:17/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
32:17/0/custom_data_1 = true
33:17/0 = 0
33:17/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
33:17/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
33:17/0/custom_data_1 = true
34:17/0 = 0
34:17/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
34:17/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
34:17/0/custom_data_1 = true
35:17/0 = 0
35:17/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
35:17/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
35:17/0/custom_data_1 = true
36:17/0 = 0
36:17/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
36:17/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
36:17/0/custom_data_1 = true
37:17/0 = 0
37:17/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
37:17/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
37:17/0/custom_data_1 = true
38:17/0 = 0
38:17/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
38:17/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
38:17/0/custom_data_1 = true
39:17/0 = 0
39:17/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
39:17/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
39:17/0/custom_data_1 = true
40:17/0 = 0
40:17/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
40:17/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
40:17/0/custom_data_1 = true
41:17/0 = 0
41:17/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
41:17/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
41:17/0/custom_data_1 = true
42:17/0 = 0
42:17/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
42:17/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
42:17/0/custom_data_1 = true
43:17/0 = 0
43:17/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
43:17/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
43:17/0/custom_data_1 = true
44:17/0 = 0
44:17/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
44:17/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
44:17/0/custom_data_1 = true
45:17/0 = 0
45:17/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
45:17/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
45:17/0/custom_data_1 = true
46:17/0 = 0
46:17/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
46:17/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
46:17/0/custom_data_1 = true
47:17/0 = 0
47:17/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
47:17/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
47:17/0/custom_data_1 = true
48:17/0 = 0
48:17/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
48:17/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
48:17/0/custom_data_1 = true
0:18/0 = 0
0:18/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
0:18/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
0:18/0/custom_data_1 = true
1:18/0 = 0
1:18/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
1:18/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
1:18/0/custom_data_1 = true
2:18/0 = 0
2:18/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
2:18/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
2:18/0/custom_data_1 = true
3:18/0 = 0
3:18/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
3:18/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
3:18/0/custom_data_1 = true
4:18/0 = 0
4:18/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
4:18/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
4:18/0/custom_data_1 = true
5:18/0 = 0
5:18/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
5:18/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
5:18/0/custom_data_1 = true
6:18/0 = 0
6:18/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
6:18/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
6:18/0/custom_data_1 = true
7:18/0 = 0
7:18/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
7:18/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
7:18/0/custom_data_1 = true
8:18/0 = 0
8:18/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
8:18/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
8:18/0/custom_data_1 = true
9:18/0 = 0
9:18/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
9:18/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
9:18/0/custom_data_1 = true
10:18/0 = 0
10:18/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
10:18/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
10:18/0/custom_data_1 = true
11:18/0 = 0
11:18/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
11:18/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
11:18/0/custom_data_1 = true
12:18/0 = 0
12:18/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
12:18/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
12:18/0/custom_data_1 = true
13:18/0 = 0
13:18/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
13:18/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
13:18/0/custom_data_1 = true
14:18/0 = 0
14:18/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
14:18/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
14:18/0/custom_data_1 = true
15:18/0 = 0
15:18/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
15:18/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
15:18/0/custom_data_1 = true
16:18/0 = 0
16:18/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
16:18/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
16:18/0/custom_data_1 = true
17:18/0 = 0
17:18/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
17:18/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
17:18/0/custom_data_1 = true
18:18/0 = 0
18:18/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
18:18/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
18:18/0/custom_data_1 = true
19:18/0 = 0
19:18/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
19:18/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
19:18/0/custom_data_1 = true
20:18/0 = 0
20:18/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
20:18/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
20:18/0/custom_data_1 = true
21:18/0 = 0
21:18/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
21:18/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
21:18/0/custom_data_1 = true
22:18/0 = 0
22:18/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
22:18/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
22:18/0/custom_data_1 = true
23:18/0 = 0
23:18/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
23:18/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
23:18/0/custom_data_1 = true
24:18/0 = 0
24:18/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
24:18/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
24:18/0/custom_data_1 = true
25:18/0 = 0
25:18/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
25:18/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
25:18/0/custom_data_1 = true
26:18/0 = 0
26:18/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
26:18/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
26:18/0/custom_data_1 = true
27:18/0 = 0
27:18/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
27:18/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
27:18/0/custom_data_1 = true
28:18/0 = 0
28:18/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
28:18/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
28:18/0/custom_data_1 = true
29:18/0 = 0
29:18/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
29:18/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
29:18/0/custom_data_1 = true
30:18/0 = 0
30:18/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
30:18/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
30:18/0/custom_data_1 = true
31:18/0 = 0
31:18/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
31:18/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
31:18/0/custom_data_1 = true
32:18/0 = 0
32:18/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
32:18/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
32:18/0/custom_data_1 = true
33:18/0 = 0
33:18/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
33:18/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
33:18/0/custom_data_1 = true
34:18/0 = 0
34:18/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
34:18/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
34:18/0/custom_data_1 = true
35:18/0 = 0
35:18/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
35:18/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
35:18/0/custom_data_1 = true
36:18/0 = 0
36:18/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
36:18/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
36:18/0/custom_data_1 = true
37:18/0 = 0
37:18/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
37:18/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
37:18/0/custom_data_1 = true
38:18/0 = 0
38:18/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
38:18/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
38:18/0/custom_data_1 = true
39:18/0 = 0
39:18/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
39:18/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
39:18/0/custom_data_1 = true
40:18/0 = 0
40:18/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
40:18/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
40:18/0/custom_data_1 = true
41:18/0 = 0
41:18/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
41:18/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
41:18/0/custom_data_1 = true
42:18/0 = 0
42:18/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
42:18/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
42:18/0/custom_data_1 = true
43:18/0 = 0
43:18/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
43:18/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
43:18/0/custom_data_1 = true
44:18/0 = 0
44:18/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
44:18/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
44:18/0/custom_data_1 = true
45:18/0 = 0
45:18/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
45:18/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
45:18/0/custom_data_1 = true
46:18/0 = 0
46:18/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
46:18/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
46:18/0/custom_data_1 = true
47:18/0 = 0
47:18/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
47:18/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
47:18/0/custom_data_1 = true
48:18/0 = 0
48:18/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
48:18/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
48:18/0/custom_data_1 = true
0:19/0 = 0
0:19/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
0:19/0/physics_layer_0/polygon_0/points = PackedVector2Array(0, -8, 8, 0, 8, 8, -8, 8, -8, 0)
0:19/0/custom_data_1 = true
1:19/0 = 0
1:19/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
1:19/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
1:19/0/custom_data_1 = true
2:19/0 = 0
2:19/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
2:19/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
2:19/0/custom_data_1 = true
3:19/0 = 0
3:19/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
3:19/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
3:19/0/custom_data_1 = true
4:19/0 = 0
4:19/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
4:19/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
4:19/0/custom_data_1 = true
5:19/0 = 0
5:19/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
5:19/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
5:19/0/custom_data_1 = true
6:19/0 = 0
6:19/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
6:19/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
6:19/0/custom_data_1 = true
7:19/0 = 0
7:19/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
7:19/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
7:19/0/custom_data_1 = true
8:19/0 = 0
8:19/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
8:19/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
8:19/0/custom_data_1 = true
9:19/0 = 0
9:19/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
9:19/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
9:19/0/custom_data_1 = true
10:19/0 = 0
10:19/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
10:19/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
10:19/0/custom_data_1 = true
11:19/0 = 0
11:19/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
11:19/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
11:19/0/custom_data_1 = true
12:19/0 = 0
12:19/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
12:19/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
12:19/0/custom_data_1 = true
13:19/0 = 0
13:19/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
13:19/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
13:19/0/custom_data_1 = true
14:19/0 = 0
14:19/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
14:19/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
14:19/0/custom_data_1 = true
15:19/0 = 0
15:19/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
15:19/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
15:19/0/custom_data_1 = true
16:19/0 = 0
16:19/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
16:19/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
16:19/0/custom_data_1 = true
17:19/0 = 0
17:19/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
17:19/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
17:19/0/custom_data_1 = true
18:19/0 = 0
18:19/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
18:19/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
18:19/0/custom_data_1 = true
19:19/0 = 0
19:19/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
19:19/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
19:19/0/custom_data_1 = true
20:19/0 = 0
20:19/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
20:19/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
20:19/0/custom_data_1 = true
21:19/0 = 0
21:19/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
21:19/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
21:19/0/custom_data_1 = true
22:19/0 = 0
22:19/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
22:19/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
22:19/0/custom_data_1 = true
23:19/0 = 0
23:19/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
23:19/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
23:19/0/custom_data_1 = true
24:19/0 = 0
24:19/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
24:19/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
24:19/0/custom_data_1 = true
25:19/0 = 0
25:19/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
25:19/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
25:19/0/custom_data_1 = true
26:19/0 = 0
26:19/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
26:19/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
26:19/0/custom_data_1 = true
27:19/0 = 0
27:19/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
27:19/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
27:19/0/custom_data_1 = true
28:19/0 = 0
28:19/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
28:19/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
28:19/0/custom_data_1 = true
29:19/0 = 0
29:19/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
29:19/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
29:19/0/custom_data_1 = true
30:19/0 = 0
30:19/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
30:19/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
30:19/0/custom_data_1 = true
31:19/0 = 0
31:19/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
31:19/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
31:19/0/custom_data_1 = true
32:19/0 = 0
32:19/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
32:19/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
32:19/0/custom_data_1 = true
33:19/0 = 0
33:19/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
33:19/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
33:19/0/custom_data_1 = true
34:19/0 = 0
34:19/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
34:19/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
34:19/0/custom_data_1 = true
35:19/0 = 0
35:19/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
35:19/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
35:19/0/custom_data_1 = true
36:19/0 = 0
36:19/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
36:19/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
36:19/0/custom_data_1 = true
37:19/0 = 0
37:19/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
37:19/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
37:19/0/custom_data_1 = true
38:19/0 = 0
38:19/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
38:19/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
38:19/0/custom_data_1 = true
39:19/0 = 0
39:19/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
39:19/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
39:19/0/custom_data_1 = true
40:19/0 = 0
40:19/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
40:19/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
40:19/0/custom_data_1 = true
41:19/0 = 0
41:19/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
41:19/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
41:19/0/custom_data_1 = true
42:19/0 = 0
42:19/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
42:19/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
42:19/0/custom_data_1 = true
43:19/0 = 0
43:19/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
43:19/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
43:19/0/custom_data_1 = true
44:19/0 = 0
44:19/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
44:19/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
44:19/0/custom_data_1 = true
45:19/0 = 0
45:19/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
45:19/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
45:19/0/custom_data_1 = true
46:19/0 = 0
46:19/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
46:19/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
46:19/0/custom_data_1 = true
47:19/0 = 0
47:19/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
47:19/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
47:19/0/custom_data_1 = true
48:19/0 = 0
48:19/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
48:19/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
48:19/0/custom_data_1 = true
0:20/0 = 0
0:20/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
0:20/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
0:20/0/custom_data_1 = true
1:20/0 = 0
1:20/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
1:20/0/physics_layer_0/polygon_0/points = PackedVector2Array(0, -8, 8, 0, 8, 8, -8, 8, -8, 0)
1:20/0/custom_data_1 = true
2:20/0 = 0
2:20/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
2:20/0/physics_layer_0/polygon_0/points = PackedVector2Array(0, -8, 8, 0, 8, 8, -8, 8, -8, 0)
2:20/0/custom_data_1 = true
3:20/0 = 0
3:20/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
3:20/0/physics_layer_0/polygon_0/points = PackedVector2Array(0, -8, 8, 0, 8, 8, -8, 8, -8, 0)
3:20/0/custom_data_1 = true
4:20/0 = 0
4:20/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
4:20/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
4:20/0/custom_data_1 = true
5:20/0 = 0
5:20/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
5:20/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
5:20/0/custom_data_1 = true
6:20/0 = 0
6:20/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
6:20/0/physics_layer_0/polygon_0/points = PackedVector2Array(0, -8, 8, 0, 8, 8, -8, 8, -8, 0)
6:20/0/custom_data_1 = true
7:20/0 = 0
7:20/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
7:20/0/physics_layer_0/polygon_0/points = PackedVector2Array(0, -8, 8, 0, 8, 8, -8, 8, -8, 0)
7:20/0/custom_data_1 = true
8:20/0 = 0
8:20/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
8:20/0/physics_layer_0/polygon_0/points = PackedVector2Array(0, -8, 8, 0, 8, 8, -8, 8, -8, 0)
8:20/0/custom_data_1 = true
9:20/0 = 0
9:20/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
9:20/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
9:20/0/custom_data_1 = true
10:20/0 = 0
10:20/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
10:20/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
10:20/0/custom_data_1 = true
11:20/0 = 0
11:20/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
11:20/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
11:20/0/custom_data_1 = true
12:20/0 = 0
12:20/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
12:20/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
12:20/0/custom_data_1 = true
13:20/0 = 0
13:20/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
13:20/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
13:20/0/custom_data_1 = true
14:20/0 = 0
14:20/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
14:20/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
14:20/0/custom_data_1 = true
15:20/0 = 0
15:20/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
15:20/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
15:20/0/custom_data_1 = true
16:20/0 = 0
16:20/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
16:20/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
16:20/0/custom_data_1 = true
17:20/0 = 0
17:20/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
17:20/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
17:20/0/custom_data_1 = true
18:20/0 = 0
18:20/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
18:20/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
18:20/0/custom_data_1 = true
19:20/0 = 0
19:20/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
19:20/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
19:20/0/custom_data_0 = true
20:20/0 = 0
20:20/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
20:20/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
20:20/0/custom_data_0 = true
21:20/0 = 0
21:20/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
21:20/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
21:20/0/custom_data_0 = true
22:20/0 = 0
22:20/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
22:20/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
22:20/0/custom_data_0 = true
23:20/0 = 0
23:20/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
23:20/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
23:20/0/custom_data_0 = true
24:20/0 = 0
24:20/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
24:20/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
24:20/0/custom_data_0 = true
25:20/0 = 0
25:20/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
25:20/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
25:20/0/custom_data_0 = true
26:20/0 = 0
26:20/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
26:20/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
26:20/0/custom_data_0 = true
27:20/0 = 0
27:20/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
27:20/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
27:20/0/custom_data_0 = true
28:20/0 = 0
28:20/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
28:20/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
28:20/0/custom_data_0 = true
29:20/0 = 0
29:20/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
29:20/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
29:20/0/custom_data_0 = true
30:20/0 = 0
30:20/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
30:20/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
30:20/0/custom_data_0 = true
31:20/0 = 0
31:20/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
31:20/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
31:20/0/custom_data_0 = true
32:20/0 = 0
32:20/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
32:20/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
32:20/0/custom_data_1 = true
33:20/0 = 0
33:20/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
33:20/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
33:20/0/custom_data_1 = true
34:20/0 = 0
34:20/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
34:20/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
34:20/0/custom_data_1 = true
35:20/0 = 0
35:20/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
35:20/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
35:20/0/custom_data_1 = true
36:20/0 = 0
36:20/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
36:20/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
36:20/0/custom_data_1 = true
37:20/0 = 0
37:20/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
37:20/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
37:20/0/custom_data_1 = true
38:20/0 = 0
38:20/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
38:20/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
38:20/0/custom_data_1 = true
39:20/0 = 0
39:20/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
39:20/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
39:20/0/custom_data_1 = true
40:20/0 = 0
40:20/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
40:20/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
40:20/0/custom_data_1 = true
41:20/0 = 0
41:20/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
41:20/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
41:20/0/custom_data_1 = true
42:20/0 = 0
42:20/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
42:20/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
42:20/0/custom_data_1 = true
43:20/0 = 0
43:20/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
43:20/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
43:20/0/custom_data_1 = true
44:20/0 = 0
44:20/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
44:20/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
44:20/0/custom_data_1 = true
45:20/0 = 0
45:20/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
45:20/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
45:20/0/custom_data_1 = true
46:20/0 = 0
46:20/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
46:20/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
46:20/0/custom_data_1 = true
47:20/0 = 0
47:20/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
47:20/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
47:20/0/custom_data_1 = true
48:20/0 = 0
48:20/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
48:20/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
48:20/0/custom_data_1 = true
0:21/0 = 0
0:21/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
0:21/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
0:21/0/custom_data_1 = true
1:21/0 = 0
1:21/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
1:21/0/physics_layer_0/polygon_0/points = PackedVector2Array(0, -8, 8, 0, 8, 8, -8, 8, -8, 0)
1:21/0/custom_data_1 = true
2:21/0 = 0
2:21/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
2:21/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
2:21/0/custom_data_1 = true
3:21/0 = 0
3:21/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
3:21/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
3:21/0/custom_data_1 = true
4:21/0 = 0
4:21/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
4:21/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
4:21/0/custom_data_1 = true
5:21/0 = 0
5:21/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
5:21/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
5:21/0/custom_data_1 = true
6:21/0 = 0
6:21/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
6:21/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
6:21/0/custom_data_1 = true
7:21/0 = 0
7:21/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
7:21/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
7:21/0/custom_data_1 = true
8:21/0 = 0
8:21/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
8:21/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
8:21/0/custom_data_1 = true
9:21/0 = 0
9:21/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
9:21/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
9:21/0/custom_data_1 = true
10:21/0 = 0
10:21/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
10:21/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
10:21/0/custom_data_1 = true
11:21/0 = 0
11:21/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
11:21/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
11:21/0/custom_data_1 = true
12:21/0 = 0
12:21/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
12:21/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
12:21/0/custom_data_1 = true
13:21/0 = 0
13:21/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
13:21/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
13:21/0/custom_data_1 = true
14:21/0 = 0
14:21/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
14:21/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
14:21/0/custom_data_1 = true
15:21/0 = 0
15:21/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
15:21/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
15:21/0/custom_data_1 = true
16:21/0 = 0
16:21/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
16:21/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
16:21/0/custom_data_1 = true
17:21/0 = 0
17:21/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
17:21/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
17:21/0/custom_data_1 = true
18:21/0 = 0
18:21/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
18:21/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
18:21/0/custom_data_1 = true
19:21/0 = 0
19:21/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
19:21/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
19:21/0/custom_data_0 = true
20:21/0 = 0
20:21/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
20:21/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
20:21/0/custom_data_0 = true
21:21/0 = 0
21:21/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
21:21/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
21:21/0/custom_data_0 = true
22:21/0 = 0
22:21/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
22:21/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
22:21/0/custom_data_0 = true
23:21/0 = 0
23:21/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
23:21/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
23:21/0/custom_data_0 = true
24:21/0 = 0
24:21/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
24:21/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
24:21/0/custom_data_0 = true
25:21/0 = 0
25:21/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
25:21/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
25:21/0/custom_data_0 = true
26:21/0 = 0
26:21/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
26:21/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
26:21/0/custom_data_0 = true
27:21/0 = 0
27:21/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
27:21/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
27:21/0/custom_data_0 = true
28:21/0 = 0
28:21/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
28:21/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
28:21/0/custom_data_0 = true
29:21/0 = 0
29:21/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
29:21/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
29:21/0/custom_data_0 = true
30:21/0 = 0
30:21/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
30:21/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
30:21/0/custom_data_0 = true
31:21/0 = 0
31:21/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
31:21/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
31:21/0/custom_data_0 = true
32:21/0 = 0
32:21/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
32:21/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
32:21/0/custom_data_1 = true
33:21/0 = 0
33:21/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
33:21/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
33:21/0/custom_data_1 = true
34:21/0 = 0
34:21/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
34:21/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
34:21/0/custom_data_1 = true
35:21/0 = 0
35:21/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
35:21/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
35:21/0/custom_data_1 = true
36:21/0 = 0
36:21/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
36:21/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
36:21/0/custom_data_1 = true
37:21/0 = 0
37:21/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
37:21/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
37:21/0/custom_data_1 = true
38:21/0 = 0
38:21/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
38:21/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
38:21/0/custom_data_1 = true
39:21/0 = 0
39:21/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
39:21/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
39:21/0/custom_data_1 = true
40:21/0 = 0
40:21/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
40:21/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
40:21/0/custom_data_1 = true
41:21/0 = 0
41:21/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
41:21/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
41:21/0/custom_data_1 = true
42:21/0 = 0
42:21/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
42:21/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
42:21/0/custom_data_1 = true
43:21/0 = 0
43:21/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
43:21/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
43:21/0/custom_data_1 = true
44:21/0 = 0
44:21/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
44:21/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
44:21/0/custom_data_1 = true
45:21/0 = 0
45:21/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
45:21/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
45:21/0/custom_data_1 = true
46:21/0 = 0
46:21/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
46:21/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
46:21/0/custom_data_1 = true
47:21/0 = 0
47:21/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
47:21/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
47:21/0/custom_data_1 = true
48:21/0 = 0
48:21/0/occlusion_layer_0/polygon_0/polygon = SubResource("OccluderPolygon2D_2h6ev")
48:21/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
48:21/0/custom_data_1 = true

[resource]
occlusion_layer_0/light_mask = 1
physics_layer_0/collision_layer = 16
physics_layer_0/collision_mask = 0
custom_data_layer_0/name = "isWalkable"
custom_data_layer_0/type = 1
custom_data_layer_1/name = "isBlocked"
custom_data_layer_1/type = 1
sources/0 = SubResource("TileSetAtlasSource_ay4j7")

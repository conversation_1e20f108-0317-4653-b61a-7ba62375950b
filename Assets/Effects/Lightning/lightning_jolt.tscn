[gd_scene load_steps=8 format=3 uid="uid://hgqp11j3720b"]

[ext_resource type="Script" uid="uid://ddrj0uvlpb55x" path="res://Assets/Effects/Lightning/lightning_jolt.gd" id="1"]

[sub_resource type="Curve" id="1"]
_data = [Vector2(0, 1), 0.0, 0.23288, 0, 0, Vector2(1, 0.615385), 0.0, 0.0, 0, 0]
point_count = 2

[sub_resource type="Animation" id="2"]
length = 2.0
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath(".:self_modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.2, 0.5),
"transitions": PackedFloat32Array(3.03143, 3.03143, 1),
"update": 0,
"values": [Color(1, 1, 1, 1), Color(1, 1, 1, 1), Color(1, 1, 1, 0)]
}
tracks/1/type = "method"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath(".")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(2),
"transitions": PackedFloat32Array(1),
"values": [{
"args": [],
"method": &"queue_free"
}]
}
tracks/2/type = "value"
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/path = NodePath("Sparks:emitting")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_xav8d"]
_data = {
&"destroy": SubResource("2")
}

[sub_resource type="Curve" id="3"]
_data = [Vector2(0, 1), 0.0, -0.0819979, 0, 0, Vector2(0.586319, 0.817308), -1.31782, -1.31782, 0, 0, Vector2(1, 0), 0.0, 0.0, 0, 0]
point_count = 3

[sub_resource type="CurveTexture" id="4"]
curve = SubResource("3")

[sub_resource type="ParticleProcessMaterial" id="5"]
direction = Vector3(0, 0, 0)
spread = 180.0
gravity = Vector3(0, 0, 0)
scale_min = 0.0
scale_max = 0.0
scale_curve = SubResource("4")
color = Color(0.482353, 0.662745, 1, 1)

[node name="LightningJolt2D" type="Line2D"]
modulate = Color(1.3, 1.3, 1.3, 1)
self_modulate = Color(1, 1, 1, 0)
width = 6.0
width_curve = SubResource("1")
default_color = Color(0.482353, 0.662745, 1, 1)
begin_cap_mode = 2
end_cap_mode = 2
script = ExtResource("1")
spread_angle = 1.0

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
libraries = {
&"": SubResource("AnimationLibrary_xav8d")
}
autoplay = "destroy"

[node name="Sparks" type="GPUParticles2D" parent="."]
self_modulate = Color(1.1, 1.1, 1.1, 1)
emitting = false
amount = 4
one_shot = true
explosiveness = 1.0
randomness = 1.0
process_material = SubResource("5")

[node name="RayCast2D" type="RayCast2D" parent="."]

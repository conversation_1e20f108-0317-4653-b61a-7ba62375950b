[gd_scene load_steps=12 format=3 uid="uid://cb1jy3pa1b4tk"]

[ext_resource type="Script" path="res://laser_beam/laser_beam_2d.gd" id="1"]
[ext_resource type="Texture2D" uid="uid://t105tdvgunrx" path="res://assets/glowing_circle.png" id="2"]

[sub_resource type="Gradient" id="1"]
offsets = PackedFloat32Array(0.582915, 1)
colors = PackedColorArray(1, 1, 1, 1, 1, 1, 1, 0)

[sub_resource type="GradientTexture2D" id="2"]
gradient = SubResource("1")

[sub_resource type="Curve" id="3"]
_data = [Vector2(0.518072, 1), 0.0, -3.53434, 0, 0, Vector2(1, 0), 0.0, 0.0, 0, 0]
point_count = 2

[sub_resource type="CurveTexture" id="4"]
width = 2048
curve = SubResource("3")

[sub_resource type="ParticleProcessMaterial" id="5"]
initial_velocity_min = 100.0
initial_velocity_max = 100.0
gravity = Vector3(0, 0, 0)
scale_min = 0.5
scale_max = 0.5
scale_curve = SubResource("4")
color_ramp = SubResource("2")

[sub_resource type="Curve" id="6"]
_data = [Vector2(0, 0), 0.0, 0.0, 0, 0, Vector2(0.503614, 0.957505), 0.0, 0.0, 0, 0, Vector2(1, 0), 0.0, 0.0, 0, 0]
point_count = 3

[sub_resource type="CurveTexture" id="7"]
curve = SubResource("6")

[sub_resource type="ParticleProcessMaterial" id="8"]
particle_flag_align_y = true
particle_flag_disable_z = true
emission_shape = 3
emission_box_extents = Vector3(60, 20, 1)
direction = Vector3(-1, 0, 0)
spread = 0.0
gravity = Vector3(0, 0, 0)
tangential_accel_min = 100.0
tangential_accel_max = 100.0
scale_min = 0.3
scale_max = 0.3
scale_curve = SubResource("7")

[sub_resource type="ParticleProcessMaterial" id="9"]
particle_flag_disable_z = true
spread = 50.0
initial_velocity_min = 300.0
initial_velocity_max = 300.0
gravity = Vector3(0, 0, 0)
scale_min = 0.5
scale_max = 0.5
scale_curve = SubResource("4")
color_ramp = SubResource("2")

[node name="LaserBeam2D" type="RayCast2D"]
target_position = Vector2(0, 0)
script = ExtResource("1")

[node name="FillLine2D" type="Line2D" parent="."]
modulate = Color(1.5, 1.5, 1.5, 1)
points = PackedVector2Array(0, 0, 100, 0)
default_color = Color(0.301961, 0.65098, 1, 1)
texture_mode = 2
joint_mode = 2
begin_cap_mode = 2
end_cap_mode = 2
sharp_limit = 8.0
antialiased = true

[node name="CastingParticles2D" type="GPUParticles2D" parent="."]
modulate = Color(1.5, 1.5, 1.5, 1)
show_behind_parent = true
emitting = false
process_material = SubResource("5")
texture = ExtResource("2")
lifetime = 0.3
visibility_rect = Rect2(0, -18.722, 29.6756, 38.4841)

[node name="BeamParticles2D" type="GPUParticles2D" parent="."]
modulate = Color(1.5, 1.5, 1.5, 1)
emitting = false
amount = 50
process_material = SubResource("8")
texture = ExtResource("2")
preprocess = 1.0
randomness = 1.0
visibility_rect = Rect2(-2500, -2500, 5000, 5000)

[node name="CollisionParticles2D" type="GPUParticles2D" parent="."]
modulate = Color(1.5, 1.5, 1.5, 1)
show_behind_parent = true
emitting = false
amount = 16
process_material = SubResource("9")
texture = ExtResource("2")
lifetime = 0.3
visibility_rect = Rect2(-2500, -2500, 5000, 5000)

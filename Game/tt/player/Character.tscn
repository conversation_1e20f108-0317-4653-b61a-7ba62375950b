[gd_scene load_steps=83 format=3 uid="uid://bkmgrwrrjvv01"]

[ext_resource type="Script" uid="uid://bop4g7xeebnsc" path="res://Game/tt/player/Character.gd" id="1_abuih"]
[ext_resource type="Texture2D" uid="uid://75phbbptojr" path="res://Game/tt/assets/images/new/player/B/1.png" id="2_7hw2i"]
[ext_resource type="Texture2D" uid="uid://bcl6jxf2jebj4" path="res://Game/tt/assets/images/new/player/B/2.png" id="3_i7bwu"]
[ext_resource type="Texture2D" uid="uid://b20pq0vh6x2hf" path="res://Game/tt/assets/images/new/player/B/3.png" id="4_hwnip"]
[ext_resource type="Texture2D" uid="uid://btpvvdxisjwli" path="res://Game/tt/assets/images/new/player/B/4.png" id="5_aywk0"]
[ext_resource type="Texture2D" uid="uid://bwp3la4s8f66e" path="res://Game/tt/assets/images/new/player/B/5.png" id="6_uy5ap"]
[ext_resource type="Texture2D" uid="uid://he4k8j2isr03" path="res://Game/tt/assets/images/new/player/B/6.png" id="7_ujtd5"]
[ext_resource type="Texture2D" uid="uid://i3kc46f278dj" path="res://Game/tt/assets/images/new/player/B/7.png" id="8_q03gg"]
[ext_resource type="Texture2D" uid="uid://d4nb33q3vifu2" path="res://Game/tt/assets/images/new/player/B/8.png" id="9_fcw5n"]
[ext_resource type="Texture2D" uid="uid://dkkkdnxrbtoem" path="res://Game/tt/assets/images/new/player/B/9.png" id="10_uvykq"]
[ext_resource type="Texture2D" uid="uid://cxyv08a5m7to5" path="res://Game/tt/assets/images/new/player/B/10.png" id="11_23jep"]
[ext_resource type="Texture2D" uid="uid://b8uesuw0alca" path="res://Game/tt/assets/images/new/player/B/11.png" id="12_s1p1s"]
[ext_resource type="Texture2D" uid="uid://m4k58ougqova" path="res://Game/tt/assets/images/new/player/B/12.png" id="13_go1fm"]
[ext_resource type="Texture2D" uid="uid://rlhmuutut8lv" path="res://Game/tt/assets/images/new/player/B/13.png" id="14_oavmf"]
[ext_resource type="Texture2D" uid="uid://ccg6rmugxwaky" path="res://Game/tt/assets/images/new/player/B/14.png" id="15_ysq4e"]
[ext_resource type="Texture2D" uid="uid://bqy1m8vn48ldh" path="res://Game/tt/assets/images/new/player/RB/1.png" id="16_xgfd3"]
[ext_resource type="Texture2D" uid="uid://wv1kmwsoeq4b" path="res://Game/tt/assets/images/new/player/RB/2.png" id="17_bribn"]
[ext_resource type="Texture2D" uid="uid://c3rhvwouo18v" path="res://Game/tt/assets/images/new/player/RB/3.png" id="18_f4gdo"]
[ext_resource type="Texture2D" uid="uid://bv4g0r37dbdjn" path="res://Game/tt/assets/images/new/player/RB/4.png" id="19_2ehqt"]
[ext_resource type="Texture2D" uid="uid://cjxjdt5eoxevk" path="res://Game/tt/assets/images/new/player/RB/5.png" id="20_xx5r6"]
[ext_resource type="Texture2D" uid="uid://b45kar3ekppa1" path="res://Game/tt/assets/images/new/player/RB/6.png" id="21_75vmc"]
[ext_resource type="Texture2D" uid="uid://sk2pyg7wschg" path="res://Game/tt/assets/images/new/player/RB/7.png" id="22_damad"]
[ext_resource type="Texture2D" uid="uid://d0cwnfcdl841a" path="res://Game/tt/assets/images/new/player/RB/8.png" id="23_ibchh"]
[ext_resource type="Texture2D" uid="uid://cy6jpgldmugla" path="res://Game/tt/assets/images/new/player/RB/9.png" id="24_qq1no"]
[ext_resource type="Texture2D" uid="uid://m8pjefh6xjbk" path="res://Game/tt/assets/images/new/player/RB/10.png" id="25_3tuq4"]
[ext_resource type="Texture2D" uid="uid://dhs4th6kg408g" path="res://Game/tt/assets/images/new/player/RB/11.png" id="26_s7k4d"]
[ext_resource type="Texture2D" uid="uid://dy42grcdi25xq" path="res://Game/tt/assets/images/new/player/RB/12.png" id="27_hxlkx"]
[ext_resource type="Texture2D" uid="uid://dlj58ygdjqnm3" path="res://Game/tt/assets/images/new/player/R/1.png" id="28_v2mmt"]
[ext_resource type="Texture2D" uid="uid://dhaoc86tgu5g8" path="res://Game/tt/assets/images/new/player/R/2.png" id="29_je2nt"]
[ext_resource type="Texture2D" uid="uid://lnj1vknmoc26" path="res://Game/tt/assets/images/new/player/R/3.png" id="30_qbjn6"]
[ext_resource type="Texture2D" uid="uid://dtquewvvkrr8p" path="res://Game/tt/assets/images/new/player/R/4.png" id="31_qkuw3"]
[ext_resource type="Texture2D" uid="uid://wqx1kavrjc14" path="res://Game/tt/assets/images/new/player/R/5.png" id="32_6ko8r"]
[ext_resource type="Texture2D" uid="uid://s278anddp557" path="res://Game/tt/assets/images/new/player/R/6.png" id="33_0je5c"]
[ext_resource type="Texture2D" uid="uid://dmdboqgaterrc" path="res://Game/tt/assets/images/new/player/R/7.png" id="34_5v6bh"]
[ext_resource type="Texture2D" uid="uid://bhr0co4wy540k" path="res://Game/tt/assets/images/new/player/R/8.png" id="35_1q3yj"]
[ext_resource type="Texture2D" uid="uid://bosswd44fx8vq" path="res://Game/tt/assets/images/new/player/R/9.png" id="36_dy5tu"]
[ext_resource type="Texture2D" uid="uid://ciibviqg7n66n" path="res://Game/tt/assets/images/new/player/R/10.png" id="37_5df0i"]
[ext_resource type="Texture2D" uid="uid://bkdey2m2hlupf" path="res://Game/tt/assets/images/new/player/R/11.png" id="38_x5crn"]
[ext_resource type="Texture2D" uid="uid://dlxvbdbsqwrgt" path="res://Game/tt/assets/images/new/player/R/12.png" id="39_bbedt"]
[ext_resource type="Texture2D" uid="uid://b73gc4gn6apu2" path="res://Game/tt/assets/images/new/player/T/1.png" id="40_5520h"]
[ext_resource type="Texture2D" uid="uid://dgmfkk0x1me26" path="res://Game/tt/assets/images/new/player/T/2.png" id="41_d70fi"]
[ext_resource type="Texture2D" uid="uid://pejx5r3vnqb" path="res://Game/tt/assets/images/new/player/T/3.png" id="42_jwvfj"]
[ext_resource type="Texture2D" uid="uid://cqcsktsoslmy3" path="res://Game/tt/assets/images/new/player/T/4.png" id="43_ffdes"]
[ext_resource type="Texture2D" uid="uid://bj2herqg0gixk" path="res://Game/tt/assets/images/new/player/T/5.png" id="44_dr7k1"]
[ext_resource type="Texture2D" uid="uid://cw0sjyvt24gt4" path="res://Game/tt/assets/images/new/player/T/6.png" id="45_t338k"]
[ext_resource type="Texture2D" uid="uid://du7lr6q4seap3" path="res://Game/tt/assets/images/new/player/T/7.png" id="46_18gxc"]
[ext_resource type="Texture2D" uid="uid://ddbwekxy26ehg" path="res://Game/tt/assets/images/new/player/T/8.png" id="47_ttkak"]
[ext_resource type="Texture2D" uid="uid://dc6mf1qaty5gb" path="res://Game/tt/assets/images/new/player/T/9.png" id="48_x5gj3"]
[ext_resource type="Texture2D" uid="uid://bfsrdh15poit5" path="res://Game/tt/assets/images/new/player/T/10.png" id="49_qhtp8"]
[ext_resource type="Texture2D" uid="uid://dfr2ql8rt4du" path="res://Game/tt/assets/images/new/player/T/11.png" id="50_pkjf8"]
[ext_resource type="Texture2D" uid="uid://crbool6kurasa" path="res://Game/tt/assets/images/new/player/T/12.png" id="51_e8tw3"]
[ext_resource type="Texture2D" uid="uid://c47rnuvfoqmld" path="res://Game/tt/assets/images/new/player/RT/1.png" id="52_nubjt"]
[ext_resource type="Texture2D" uid="uid://vkr3wfxjjjrd" path="res://Game/tt/assets/images/new/player/RT/2.png" id="53_rsitp"]
[ext_resource type="Texture2D" uid="uid://cr4bo1vcmr6g4" path="res://Game/tt/assets/images/new/player/RT/3.png" id="54_rtym5"]
[ext_resource type="Texture2D" uid="uid://wr262vgipbj0" path="res://Game/tt/assets/images/new/player/RT/4.png" id="55_2f47f"]
[ext_resource type="Texture2D" uid="uid://b1v3vfvpk6xju" path="res://Game/tt/assets/images/new/player/RT/5.png" id="56_8o37u"]
[ext_resource type="Texture2D" uid="uid://8dkxggrv8a3s" path="res://Game/tt/assets/images/new/player/RT/6.png" id="57_r1tdo"]
[ext_resource type="Texture2D" uid="uid://c7uat8o7vhp1n" path="res://Game/tt/assets/images/new/player/RT/7.png" id="58_kmobb"]
[ext_resource type="Texture2D" uid="uid://uwdwr8dqh4wg" path="res://Game/tt/assets/images/new/player/RT/8.png" id="59_lphq6"]
[ext_resource type="Texture2D" uid="uid://b7rbxc7g4j6xa" path="res://Game/tt/assets/images/new/player/RT/9.png" id="60_6pxtw"]
[ext_resource type="Texture2D" uid="uid://d3nhwq5ub04wy" path="res://Game/tt/assets/images/new/player/RT/10.png" id="61_4al1b"]
[ext_resource type="Texture2D" uid="uid://dp26fxjtw3v3q" path="res://Game/tt/assets/images/new/player/RT/11.png" id="62_7x75v"]
[ext_resource type="Texture2D" uid="uid://4q5cblf6dnup" path="res://Game/tt/assets/images/new/player/RT/12.png" id="63_lnkw7"]
[ext_resource type="PackedScene" uid="uid://dekgkos84sljm" path="res://Components/Visual/CameraComponent.tscn" id="64_1nl5r"]
[ext_resource type="PackedScene" uid="uid://ctlquc31j3uwi" path="res://Components/Physics/OverheadPhysicsComponent.tscn" id="65_33fa4"]
[ext_resource type="Script" uid="uid://g6spk2uwn3kv" path="res://Resources/Parameters/OverheadMovementParameters.gd" id="66_f8yxx"]
[ext_resource type="PackedScene" uid="uid://dn7cpj4u7w1jy" path="res://Components/Physics/CharacterBodyComponent.tscn" id="66_l0jbw"]
[ext_resource type="PackedScene" uid="uid://caaxcyrqs036h" path="res://Game/tt/Components/Control/OverheadControlComponent8Direction.tscn" id="67_blpss"]
[ext_resource type="PackedScene" uid="uid://brb86lryxqbxy" path="res://Game/tt/enemies/Slime/Animation8DirectionComponent.tscn" id="69_irrbb"]
[ext_resource type="PackedScene" uid="uid://5tvqunkoa4l7" path="res://Components/Combat/FactionComponent.tscn" id="69_kkjmi"]
[ext_resource type="PackedScene" uid="uid://bqecemgdtqhw1" path="res://Components/Objects/CollectorComponent.tscn" id="70_collector"]
[ext_resource type="PackedScene" uid="uid://cnu5f0ycxgu8d" path="res://Components/Combat/HealthComponent.tscn" id="72_esnh3"]
[ext_resource type="Script" uid="uid://dif3dj8k5ixfw" path="res://Resources/Stat.gd" id="73_i8hkw"]
[ext_resource type="PackedScene" uid="uid://bgfxxe60d72c0" path="res://Components/Visual/HealthVisualComponent.tscn" id="74_f8yxx"]
[ext_resource type="PackedScene" uid="uid://dgt2shqy7tmrg" path="res://Game/tt/Components/Lifebar/LifeBarComponent.tscn" id="75_i8hkw"]
[ext_resource type="PackedScene" uid="uid://cd7vapsmxp183" path="res://Components/Combat/InvulnerabilityOnHitComponent.tscn" id="77_k6xay"]
[ext_resource type="PackedScene" uid="uid://ffkcrlcom1kx" path="res://Components/Combat/DamageReceivingComponent.tscn" id="77_ot2ch"]

[sub_resource type="SpriteFrames" id="SpriteFrames_dys3i"]
animations = [{
"frames": [{
"duration": 1.0,
"texture": ExtResource("2_7hw2i")
}, {
"duration": 1.0,
"texture": ExtResource("3_i7bwu")
}, {
"duration": 1.0,
"texture": ExtResource("4_hwnip")
}, {
"duration": 1.0,
"texture": ExtResource("5_aywk0")
}, {
"duration": 1.0,
"texture": ExtResource("6_uy5ap")
}, {
"duration": 1.0,
"texture": ExtResource("7_ujtd5")
}, {
"duration": 1.0,
"texture": ExtResource("8_q03gg")
}, {
"duration": 1.0,
"texture": ExtResource("9_fcw5n")
}, {
"duration": 1.0,
"texture": ExtResource("10_uvykq")
}, {
"duration": 1.0,
"texture": ExtResource("11_23jep")
}, {
"duration": 1.0,
"texture": ExtResource("12_s1p1s")
}, {
"duration": 1.0,
"texture": ExtResource("13_go1fm")
}, {
"duration": 1.0,
"texture": ExtResource("14_oavmf")
}, {
"duration": 1.0,
"texture": ExtResource("15_ysq4e")
}],
"loop": true,
"name": &"move_down",
"speed": 10.0
}, {
"frames": [{
"duration": 1.0,
"texture": ExtResource("28_v2mmt")
}, {
"duration": 1.0,
"texture": ExtResource("29_je2nt")
}, {
"duration": 1.0,
"texture": ExtResource("30_qbjn6")
}, {
"duration": 1.0,
"texture": ExtResource("31_qkuw3")
}, {
"duration": 1.0,
"texture": ExtResource("32_6ko8r")
}, {
"duration": 1.0,
"texture": ExtResource("33_0je5c")
}, {
"duration": 1.0,
"texture": ExtResource("34_5v6bh")
}, {
"duration": 1.0,
"texture": ExtResource("35_1q3yj")
}, {
"duration": 1.0,
"texture": ExtResource("36_dy5tu")
}, {
"duration": 1.0,
"texture": ExtResource("37_5df0i")
}, {
"duration": 1.0,
"texture": ExtResource("38_x5crn")
}, {
"duration": 1.0,
"texture": ExtResource("39_bbedt")
}],
"loop": true,
"name": &"move_right",
"speed": 10.0
}, {
"frames": [{
"duration": 1.0,
"texture": ExtResource("16_xgfd3")
}, {
"duration": 1.0,
"texture": ExtResource("17_bribn")
}, {
"duration": 1.0,
"texture": ExtResource("18_f4gdo")
}, {
"duration": 1.0,
"texture": ExtResource("19_2ehqt")
}, {
"duration": 1.0,
"texture": ExtResource("20_xx5r6")
}, {
"duration": 1.0,
"texture": ExtResource("21_75vmc")
}, {
"duration": 1.0,
"texture": ExtResource("22_damad")
}, {
"duration": 1.0,
"texture": ExtResource("23_ibchh")
}, {
"duration": 1.0,
"texture": ExtResource("24_qq1no")
}, {
"duration": 1.0,
"texture": ExtResource("25_3tuq4")
}, {
"duration": 1.0,
"texture": ExtResource("26_s7k4d")
}, {
"duration": 1.0,
"texture": ExtResource("27_hxlkx")
}],
"loop": true,
"name": &"move_right_down",
"speed": 10.0
}, {
"frames": [{
"duration": 1.0,
"texture": ExtResource("52_nubjt")
}, {
"duration": 1.0,
"texture": ExtResource("53_rsitp")
}, {
"duration": 1.0,
"texture": ExtResource("54_rtym5")
}, {
"duration": 1.0,
"texture": ExtResource("55_2f47f")
}, {
"duration": 1.0,
"texture": ExtResource("56_8o37u")
}, {
"duration": 1.0,
"texture": ExtResource("57_r1tdo")
}, {
"duration": 1.0,
"texture": ExtResource("58_kmobb")
}, {
"duration": 1.0,
"texture": ExtResource("59_lphq6")
}, {
"duration": 1.0,
"texture": ExtResource("60_6pxtw")
}, {
"duration": 1.0,
"texture": ExtResource("61_4al1b")
}, {
"duration": 1.0,
"texture": ExtResource("62_7x75v")
}, {
"duration": 1.0,
"texture": ExtResource("63_lnkw7")
}],
"loop": true,
"name": &"move_right_up",
"speed": 10.0
}, {
"frames": [{
"duration": 1.0,
"texture": ExtResource("40_5520h")
}, {
"duration": 1.0,
"texture": ExtResource("41_d70fi")
}, {
"duration": 1.0,
"texture": ExtResource("42_jwvfj")
}, {
"duration": 1.0,
"texture": ExtResource("43_ffdes")
}, {
"duration": 1.0,
"texture": ExtResource("44_dr7k1")
}, {
"duration": 1.0,
"texture": ExtResource("45_t338k")
}, {
"duration": 1.0,
"texture": ExtResource("46_18gxc")
}, {
"duration": 1.0,
"texture": ExtResource("47_ttkak")
}, {
"duration": 1.0,
"texture": ExtResource("48_x5gj3")
}, {
"duration": 1.0,
"texture": ExtResource("49_qhtp8")
}, {
"duration": 1.0,
"texture": ExtResource("50_pkjf8")
}, {
"duration": 1.0,
"texture": ExtResource("51_e8tw3")
}],
"loop": true,
"name": &"move_up",
"speed": 10.0
}]

[sub_resource type="RectangleShape2D" id="RectangleShape2D_7rtrr"]
size = Vector2(26, 35)

[sub_resource type="Resource" id="Resource_k6xay"]
script = ExtResource("66_f8yxx")
speed = 260.0
shouldApplyAcceleration = false
acceleration = 800.0
shouldMaintainPreviousVelocity = false
shouldMaintainMinimumVelocity = false
minimumSpeed = 100.0
shouldApplyFriction = false
friction = 1000.0
metadata/_custom_type_script = "uid://g6spk2uwn3kv"

[sub_resource type="Resource" id="Resource_ot2ch"]
resource_local_to_scene = true
resource_name = "health"
script = ExtResource("73_i8hkw")
min = 0
max = 100
value = 80
debugMode = false
name = &"health"
displayName = "Health"
description = ""

[sub_resource type="RectangleShape2D" id="RectangleShape2D_k6xay"]
size = Vector2(28, 38)

[node name="Archer" type="CharacterBody2D" groups=["entities", "players"]]
collision_layer = 6
collision_mask = 120
motion_mode = 1
wall_min_slide_angle = 0.0
script = ExtResource("1_abuih")
metadata/_custom_type_script = "uid://b3tgnb1y0hgk1"

[node name="AnimatedSprite2D" type="AnimatedSprite2D" parent="."]
scale = Vector2(0.12, 0.12)
sprite_frames = SubResource("SpriteFrames_dys3i")
animation = &"attack_down"
autoplay = "attack_down"
frame_progress = 0.219189

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
shape = SubResource("RectangleShape2D_7rtrr")

[node name="CameraComponent" parent="." instance=ExtResource("64_1nl5r")]

[node name="OverheadPhysicsComponent" parent="." instance=ExtResource("65_33fa4")]
parameters = SubResource("Resource_k6xay")

[node name="CharacterBodyComponent" parent="." instance=ExtResource("66_l0jbw")]
shouldResetVelocityIfZeroMotion = true

[node name="OverheadControlComponent8Direction" parent="." instance=ExtResource("67_blpss")]
debugMode = false

[node name="FactionComponent" parent="." instance=ExtResource("69_kkjmi")]
factions = 2

[node name="Animation8DirectionComponent" parent="." instance=ExtResource("69_irrbb")]

[node name="CollectorComponent" parent="." instance=ExtResource("70_collector")]
collision_mask = 4096

[node name="HealthComponent" parent="." instance=ExtResource("72_esnh3")]
health = SubResource("Resource_ot2ch")
shouldGameOverOnZero = true
shouldRemoveEntityOnZero = false

[node name="HealthVisualComponent" parent="." instance=ExtResource("74_f8yxx")]

[node name="LifeBarComponent" parent="." instance=ExtResource("75_i8hkw")]
offset = Vector2(0, -40)
bar_width = 68
bar_height = 14
border_width = 2
divider_width = 2
show_health_value = true
progress_color = Color(0.372549, 0.827451, 0.317647, 1)
is_player_character = true

[node name="DamageReceivingComponent" parent="." instance=ExtResource("77_ot2ch")]
collision_mask = 0

[node name="CollisionShape2D" type="CollisionShape2D" parent="DamageReceivingComponent"]
shape = SubResource("RectangleShape2D_k6xay")

[node name="InvulnerabilityOnHitComponent" parent="." instance=ExtResource("77_k6xay")]

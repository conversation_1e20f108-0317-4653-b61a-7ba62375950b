[gd_scene load_steps=30 format=3 uid="uid://unntfv0mt4qp"]

[ext_resource type="Script" uid="uid://b3tgnb1y0hgk1" path="res://Entities/Entity.gd" id="1_l1mw3"]
[ext_resource type="SpriteFrames" uid="uid://dxiw4b3bvtgwc" path="res://Game/tt/enemies/Slime/SlimeAnimation.tres" id="2_l1mw3"]
[ext_resource type="PackedScene" uid="uid://5tvqunkoa4l7" path="res://Components/Combat/FactionComponent.tscn" id="3_18pi5"]
[ext_resource type="PackedScene" uid="uid://cnu5f0ycxgu8d" path="res://Components/Combat/HealthComponent.tscn" id="4_v4t2t"]
[ext_resource type="Script" uid="uid://dif3dj8k5ixfw" path="res://Resources/Stat.gd" id="5_37adq"]
[ext_resource type="PackedScene" uid="uid://bgfxxe60d72c0" path="res://Components/Visual/HealthVisualComponent.tscn" id="6_lxef4"]
[ext_resource type="PackedScene" uid="uid://ffkcrlcom1kx" path="res://Components/Combat/DamageReceivingComponent.tscn" id="7_bisw2"]
[ext_resource type="PackedScene" uid="uid://dgt2shqy7tmrg" path="res://Game/tt/Components/Lifebar/LifeBarComponent.tscn" id="7_ddi3i"]
[ext_resource type="PackedScene" uid="uid://5cmrvf33lh2r" path="res://Components/Movement/ChaseComponent.tscn" id="8_t1cpy"]
[ext_resource type="PackedScene" uid="uid://cpklu2uieedw7" path="res://Components/Combat/DamageComponent.tscn" id="9_vfbd3"]
[ext_resource type="PackedScene" uid="uid://ctlquc31j3uwi" path="res://Components/Physics/OverheadPhysicsComponent.tscn" id="10_sahhb"]
[ext_resource type="Script" uid="uid://g6spk2uwn3kv" path="res://Resources/Parameters/OverheadMovementParameters.gd" id="11_v47i3"]
[ext_resource type="PackedScene" uid="uid://dn7cpj4u7w1jy" path="res://Components/Physics/CharacterBodyComponent.tscn" id="12_ue5hm"]
[ext_resource type="PackedScene" uid="uid://bqxvn8ywqhqxr" path="res://Game/tt/enemies/Slime/SplitOnDeathComponent.tscn" id="13_split"]
[ext_resource type="PackedScene" uid="uid://brb86lryxqbxy" path="res://Game/tt/enemies/Slime/Animation8DirectionComponent.tscn" id="14_neg4v"]
[ext_resource type="PackedScene" uid="uid://bqxvn8ywqxqxq" path="res://Game/tt/enemies/drop/RandomDropOnDeathComponent.tscn" id="15_4f2o6"]
[ext_resource type="PackedScene" uid="uid://b77c0diewkcu5" path="res://Game/tt/enemies/Slime/SmallSlime.tscn" id="16_c830l"]
[ext_resource type="PackedScene" uid="uid://b40afbahg3bm4" path="res://Game/tt/effects/death/death_effect_component.tscn" id="17_ddi3i"]
[ext_resource type="PackedScene" uid="uid://bqxvhqxqxqxqy" path="res://Game/tt/Components/sound/HitSoundComponent.tscn" id="18_405km"]
[ext_resource type="PackedScene" uid="uid://bqxvhqxqxqxqx" path="res://Game/tt/Components/sound/SoundEffectComponent.tscn" id="19_tglif"]
[ext_resource type="PackedScene" uid="uid://cqxax8y706b5d" path="res://Game/tt/Components/arrow/MonsterIndicatorComponent.tscn" id="21_71vak"]
[ext_resource type="PackedScene" uid="uid://fbr08wlva0e4" path="res://Game/tt/effects/born/BornEffects.tscn" id="21_672vc"]
[ext_resource type="PackedScene" uid="uid://ctcax3pn44pe6" path="res://Game/tt/scenes/effects/SpineSpawnEffect.tscn" id="22_5nppd"]
[ext_resource type="PackedScene" uid="uid://bx6cxxpnw2511" path="res://Components/Combat/KnockbackOnHitComponent.tscn" id="24_7nf4y"]

[sub_resource type="Resource" id="Resource_q3w6x"]
resource_local_to_scene = true
resource_name = "monsterHealth"
script = ExtResource("5_37adq")
min = 0
max = 1000
value = 1000
debugMode = false
name = &"monsterHealth"
displayName = "Monster HP"
description = ""
metadata/_custom_type_script = "uid://dif3dj8k5ixfw"

[sub_resource type="CircleShape2D" id="CircleShape2D_c830l"]
radius = 29.6875

[sub_resource type="CircleShape2D" id="CircleShape2D_tglif"]
radius = 17.5

[sub_resource type="Resource" id="Resource_u3ofw"]
script = ExtResource("11_v47i3")
speed = 60.0
shouldApplyAcceleration = true
acceleration = 50.0
shouldMaintainPreviousVelocity = false
shouldMaintainMinimumVelocity = false
minimumSpeed = 60.0
shouldApplyFriction = true
friction = 1000.0
metadata/_custom_type_script = ExtResource("11_v47i3")

[sub_resource type="CircleShape2D" id="CircleShape2D_4f2o6"]
radius = 22.5347

[node name="Slime" type="CharacterBody2D" groups=["enemies", "entities"]]
z_index = 10
collision_layer = 2
collision_mask = 80
motion_mode = 1
wall_min_slide_angle = 0.0
script = ExtResource("1_l1mw3")
isLoggingEnabled = false

[node name="AnimatedSprite2D" type="AnimatedSprite2D" parent="."]
z_index = 10
sprite_frames = ExtResource("2_l1mw3")
animation = &"born"
autoplay = "born"

[node name="FactionComponent" parent="." instance=ExtResource("3_18pi5")]
factions = 8

[node name="HealthComponent" parent="." instance=ExtResource("4_v4t2t")]
health = SubResource("Resource_q3w6x")

[node name="HealthVisualComponent" parent="." instance=ExtResource("6_lxef4")]

[node name="LifeBarComponent" parent="." instance=ExtResource("7_ddi3i")]
debugMode = true

[node name="DamageReceivingComponent" parent="." instance=ExtResource("7_bisw2")]
scale = Vector2(0.8, 0.8)

[node name="ReceivingCollisionShape" parent="DamageReceivingComponent" index="0"]
shape = SubResource("CircleShape2D_c830l")

[node name="ChaseComponent" parent="." instance=ExtResource("8_t1cpy")]

[node name="DamageComponent" parent="." instance=ExtResource("9_vfbd3")]
scale = Vector2(0.8, 0.8)
collision_layer = 256
collision_mask = 128
damageOnCollision = 10
shouldEmitBubbleOnMiss = false

[node name="DamageCollisionShape" parent="DamageComponent" index="0"]
shape = SubResource("CircleShape2D_tglif")

[node name="OverheadPhysicsComponent" parent="." instance=ExtResource("10_sahhb")]
parameters = SubResource("Resource_u3ofw")

[node name="CharacterBodyComponent" parent="." instance=ExtResource("12_ue5hm")]

[node name="Animation8DirectionComponent" parent="." instance=ExtResource("14_neg4v")]

[node name="SplitOnDeathComponent" parent="." instance=ExtResource("13_split")]
sceneToSplit = ExtResource("16_c830l")
spreadRadius = 20.0
velocityRatio = 0.6

[node name="RandomDropOnDeathComponent" parent="." instance=ExtResource("15_4f2o6")]

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
shape = SubResource("CircleShape2D_4f2o6")

[node name="DeathEffectComponent" parent="." instance=ExtResource("17_ddi3i")]

[node name="HitSoundComponent" parent="." instance=ExtResource("18_405km")]
hit_sound_path = "res://Game/tt/assets/sound/怪物受击.WAV"
death_sound_path = "res://Game/tt/assets/sound/怪物受击.WAV"

[node name="SoundEffectComponent" parent="." instance=ExtResource("19_tglif")]

[node name="MonsterIndicatorComponent" parent="." instance=ExtResource("21_71vak")]

[node name="SpineSpawnEffect" parent="MonsterIndicatorComponent" instance=ExtResource("22_5nppd")]

[node name="BornEffects" parent="." instance=ExtResource("21_672vc")]

[node name="KnockbackOnHitComponent" parent="." instance=ExtResource("24_7nf4y")]
knockbackForce = 100.0

[editable path="DamageReceivingComponent"]
[editable path="DamageComponent"]

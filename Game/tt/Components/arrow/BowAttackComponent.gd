## 弓箭攻击组件
## 基于CooldownComponent提供完整的弓箭射击功能
## 支持点击射击、冷却管理、DamageComponent集成

class_name BowAttackComponent
extends CooldownComponent

#region Parameters
@export_group("⚔️ 弓箭设置")
## 箭矢实体场景文件，必须包含ArrowProjectileComponent和DamageComponent
@export var arrowEntity: PackedScene
## 基础伤害值，会传递给箭矢的DamageComponent
@export_range(1.0, 100.0, 5.0) var baseDamage: float = 25.0
## 攻击冷却时间继承自CooldownComponent，可在场景中配置
## 射程限制（像素）
@export_range(200.0, 3000.0, 50.0) var range: float = 3000.0
## 箭矢飞行速度（像素/秒）
@export_range(200.0, 3000.0, 25.0) var arrowSpeed: float = 2000.0

@export_group("🎯 射击模式")
## 启用自动射击（启用后忽略鼠标输入）
@export var enableAutoShooting: bool = false
## 自动检测敌人范围（像素）
@export_range(50.0, 2000.0, 25.0) var autoDetectionRange: float = 2000.0
## 敌人扫描间隔（秒）
@export_range(0.1, 1.0, 0.1) var autoScanInterval: float = 0.2
## 敌人组名称
@export var enemyGroup: String = "enemies"
## 自动射击冷却时间（秒）
@export_range(0.1, 3.0, 0.1) var autoShootCooldown: float = 1.0

# 新增：敌对阵营值（可配置，默认8）
@export var enemyFactionValue: int = 8

@export_group("🔧 自动射击调试")
## 显示自动检测范围
@export var showAutoDetectionRange: bool = false
## 检测范围圆圈颜色
@export var autoRangeColor: Color = Color.YELLOW
## 检测范围线宽
@export_range(1.0, 5.0, 0.5) var autoRangeLineWidth: float = 2.0
## 显示当前自动目标连线
@export var showAutoTarget: bool = false
## 目标连线颜色
@export var autoTargetLineColor: Color = Color.RED

@export_group("🎯 控制设置")
## 是否响应玩家输入（仅手动模式有效）
@export var isPlayerControlled: bool = true
## 是否启用攻击功能
@export var isEnabled: bool = true

@export_group("🏹 发射设置")
## 箭矢发射点，如果为空则使用组件父实体位置
@export var arrowEmitter: Node2D
## 箭矢发射位置偏移
@export var arrowPositionOffset: Vector2
## 箭矢父节点覆盖，如果为空则添加到当前场景
@export var arrowParentOverride: Node

@export_group("🎯 并排发射设置")
## 启用并排发射（发射多个箭矢）
@export var enableDualShot: bool = false
## 并排箭矢数量
@export_range(2, 8, 1) var dualShotCount: int = 2
## 并排箭矢总间距（像素）
@export_range(20.0, 200.0, 10.0) var dualShotSpacing: float = 30.0
## 并排箭矢伤害衰减（百分比）
@export_range(0.0, 80.0, 5.0) var dualShotDamageReduction: float = 30.0

# 临时强制启用调试模式以便验证步骤3
@export var debugBow: bool = false

#endregion

#region 自动射击状态
## 当前自动目标
var currentTarget: Entity = null
## 上次敌人扫描时间
var lastScanTime: float = 0.0
## 自动射击计时器
var autoShootTimer: float = 0.0
## 检测到的敌人列表
var detectedEnemies: Array[Entity] = []
#endregion

#region 信号（参考EnemyDetectionComponent）
## 当检测到目标时触发
signal target_detected(target: Entity)
## 当目标丢失时触发
signal target_lost()
## 当目标改变时触发
signal target_changed(oldTarget: Entity, newTarget: Entity)
#endregion

#region Signals
## 当发射箭矢时触发
signal didFire(arrow: Entity)
## 玩家或AI主动请求攻击时调用
signal before_attack(target_position: Vector2)
#endregion

#region Dependencies
## 派系组件，用于传递给箭矢
var factionComponent: FactionComponent
## 音效组件，用于播放发射音效
var soundEffectComponent: Node = null
#endregion

func _enter_tree():
	super._enter_tree()

	# 获取依赖组件
	factionComponent = coComponents.get(&"FactionComponent")
	soundEffectComponent = get_node_or_null("SoundEffectComponent")


	if debugBow:
		printLog("BowAttackComponent: 组件初始化完成")
		printLog("  - factionComponent: " + str(factionComponent != null))
		printLog("  - enableAutoShooting: " + str(enableAutoShooting))
		printLog("  - 调试模式已强制启用用于验证")

func _ready() -> void:
	# 验证配置
	if not validateAutoShootingConfig():
		printWarning("BowAttackComponent: 自动射击配置存在问题")

	# 如果自动射击被禁用，确保清理状态
	if not enableAutoShooting:
		clearAutoTargetState()

	if debugBow:
		printLog("BowAttackComponent: 初始化完成，模式: " + getShootingMode())
	var player = get_parent()
	if player and player.has_signal("attack_animation_finished"):
		player.connect("attack_animation_finished", self._on_player_attack_animation_finished)

func _process(delta: float) -> void:
	# 保护性检查
	if not parentEntity:
		return

	# 只有启用自动射击时才运行自动逻辑
	if not enableAutoShooting or not isEnabled:
		return

	# 防止delta异常值
	if delta > 1.0:
		if debugBow:
			printWarning("BowAttackComponent: delta值异常: " + str(delta))
		return

	# 敌人检测逻辑（参考EnemyDetectionComponent模式）
	lastScanTime += delta
	if lastScanTime >= autoScanInterval:
		handleEnemyDetection()
		lastScanTime = 0.0

	# 更新自动射击
	updateAutoShooting(delta)

# TODO: 步骤4 - 可视化调试更新（将在步骤4实现）
# if showAutoDetectionRange or showAutoTarget:
#	queue_redraw()

func _unhandled_input(event: InputEvent) -> void:
	# 第一优先级：检查自动射击配置
	if enableAutoShooting:
		if debugBow:
			printLog("BowAttackComponent: 自动射击模式启用，忽略手动输入")
		return

	# 第二优先级：组件开关
	if not isEnabled:
		return

	# 第三优先级：玩家控制
	if not isPlayerControlled:
		return

	# 处理手动射击输入
	if event is InputEventMouseButton:
		if event.button_index == MOUSE_BUTTON_LEFT and event.pressed:
			fire()
			if debugBow:
				printLog("BowAttackComponent: 手动射击触发")

## 朝向指定坐标射击
func fireAtTarget(targetPosition: Vector2) -> Entity:
	# 检查当前自动目标是否已死亡
	if currentTarget and isTargetDead(currentTarget):
		if debugBow:
			printLog("BowAttackComponent: 手动射击时发现目标已死亡，清除目标")
		currentTarget = null
	
	return _internalFire(targetPosition)

## 主要攻击方法，创建并发射箭矢（朝向鼠标）
func fire() -> Entity:
	if enableAutoShooting:
		if debugBow:
			printWarning("BowAttackComponent: 自动射击模式下忽略手动fire()调用")
		return null

	return _internalFire(Vector2.ZERO)

## 内部射击方法
func _internalFire(targetPosition: Vector2 = Vector2.ZERO) -> Entity:
	# 1. 状态检查 - 自动射击时不检查 hasCooldownCompleted
	if not isEnabled:
		return null
	print("BowAttackComponent:start")
	# 只在手动射击时检查 hasCooldownCompleted
	if not enableAutoShooting and not hasCooldownCompleted:
		if debugBow:
			printLog("BowAttackComponent: 冷却中，忽略射击")
		return null

	if not arrowEntity:
		printWarning("BowAttackComponent: 未设置arrowEntity！")
		return null

	# 2. 创建箭矢
	var arrows: Array[Entity] = []

	if enableDualShot:
		# 并排发射多个箭矢
		arrows = createDualArrows(targetPosition)
	else:
		# 单发箭矢
		var singleArrow = createNewArrow(targetPosition)
		if singleArrow:
			arrows.append(singleArrow)

	if arrows.is_empty():
		printError("BowAttackComponent: 创建箭矢失败")
		return null

	# 3. 添加到场景
	var targetParent: Node = getTargetParent()
	for arrow in arrows:
		targetParent.add_child(arrow)
		arrow.owner = arrow.get_parent()

	# 4. 发射信号和启动冷却
	for arrow in arrows:
		didFire.emit(arrow)

	# 播放发射音效
	if soundEffectComponent:
		soundEffectComponent.play_sound("res://Game/tt/assets/sound/shoot.wav", 0.0)

	# 只在手动射击时启动冷却
	if not enableAutoShooting:
		startCooldown()

	if debugBow:
		printLog("BowAttackComponent: 发射箭矢成功，数量: " + str(arrows.size()) + "，伤害: " + str(baseDamage))

	# 返回第一个箭矢（保持向后兼容）
	return arrows[0] if arrows.size() > 0 else null

## 创建新的箭矢实体
func createNewArrow(targetPosition: Vector2 = Vector2.ZERO) -> Entity:
	# 1. 实例化箭矢
	var newArrow: Entity = arrowEntity.instantiate() as Entity
	if not newArrow:
		printError("BowAttackComponent: 无法实例化箭矢实体")
		return null

	# 2. 设置位置和旋转 - 实时获取位置
	var emitterPosition = getRealtimeEmitterPosition()
	newArrow.global_position = emitterPosition + arrowPositionOffset

	if debugBow:
		printLog("BowAttackComponent: 发射位置设置完成")
		printLog("  - 实时发射器位置: " + str(emitterPosition))
		printLog("  - 位置偏移: " + str(arrowPositionOffset))
		printLog("  - 最终箭矢位置: " + str(newArrow.global_position))

	# 3. 配置DamageComponent
	configureDamageComponent(newArrow)

	# 4. 配置ArrowProjectileComponent
	configureProjectileComponent(newArrow, targetPosition)

	# 5. 复制派系信息
	copyFactionToArrow(newArrow)

	return newArrow

## 创建并排的多个箭矢
func createDualArrows(targetPosition: Vector2 = Vector2.ZERO) -> Array[Entity]:
	var arrows: Array[Entity] = []

	# 获取基础射击方向
	var baseDirection = getShootDirection(targetPosition)
	var emitterPosition = getRealtimeEmitterPosition()

	# 计算并排偏移
	var perpendicularDirection = Vector2(-baseDirection.y, baseDirection.x)  # 垂直于射击方向的向量

	# 计算每个箭矢的偏移位置
	var totalSpacing = dualShotSpacing
	var spacingBetweenArrows = totalSpacing / (dualShotCount - 1) if dualShotCount > 1 else 0
	var startOffset = -totalSpacing * 0.5  # 从最左边开始

	# 创建多个箭矢（所有箭矢都朝同一个方向）
	for i in range(dualShotCount):
		var positionOffset = perpendicularDirection * (startOffset + i * spacingBetweenArrows)

		var arrow = createNewArrowWithOffset(targetPosition, positionOffset, 0.0)  # 角度偏移设为0
		if arrow:
			arrows.append(arrow)
			if debugBow:
				printLog("BowAttackComponent: 箭矢 " + str(i + 1) + " 创建成功")

	if debugBow:
		printLog("BowAttackComponent: 并排箭矢创建完成，数量: " + str(arrows.size()))
		printLog("  - 箭矢数量: " + str(dualShotCount))
		printLog("  - 总间距: " + str(dualShotSpacing) + " 像素")
		printLog("  - 所有箭矢朝同一方向飞行")
		printLog("  - 伤害衰减: " + str(dualShotDamageReduction) + "%")

	return arrows

## 创建带偏移的箭矢
func createNewArrowWithOffset(targetPosition: Vector2, positionOffset: Vector2, angleOffset: float) -> Entity:
	# 1. 实例化箭矢
	var newArrow: Entity = arrowEntity.instantiate() as Entity
	if not newArrow:
		printError("BowAttackComponent: 无法实例化箭矢实体")
		return null

	# 2. 设置位置和旋转
	var emitterPosition = getRealtimeEmitterPosition()
	newArrow.global_position = emitterPosition + arrowPositionOffset + positionOffset

	# 3. 配置DamageComponent（并排箭矢伤害衰减）
	configureDamageComponentWithReduction(newArrow)

	# 4. 配置ArrowProjectileComponent（带角度偏移）
	configureProjectileComponentWithOffset(newArrow, targetPosition, angleOffset)

	# 5. 复制派系信息
	copyFactionToArrow(newArrow)

	return newArrow

## 配置箭矢的DamageComponent
func configureDamageComponent(arrow: Entity):
	var arrowDamageComponent: DamageComponent = arrow.components.get(&"DamageComponent")
	if arrowDamageComponent:
		arrowDamageComponent.initiatorEntity = parentEntity
		arrowDamageComponent.damageOnCollision = int(baseDamage)
		arrowDamageComponent.removeEntityOnApplyingDamage = true
		arrowDamageComponent.friendlyFire = false

		if debugBow:
			printLog("BowAttackComponent: DamageComponent配置完成")
	else:
		printWarning("BowAttackComponent: 箭矢缺少DamageComponent")

## 配置箭矢的ArrowProjectileComponent
func configureProjectileComponent(arrow: Entity, targetPosition: Vector2 = Vector2.ZERO):
	var arrowProjectileComponent: ArrowProjectileComponent = arrow.components.get(&"ArrowProjectileComponent")
	if arrowProjectileComponent:
		var direction = getShootDirection(targetPosition)
		arrowProjectileComponent.launch(direction, arrowSpeed, range)
		arrow.rotation = direction.angle()

		if debugBow:
			printLog("BowAttackComponent: ArrowProjectileComponent配置完成")
			printLog("  - 方向: " + str(direction))
			printLog("  - 速度: " + str(arrowSpeed))
			printLog("  - 射程: " + str(range))
	else:
		printWarning("BowAttackComponent: 箭矢缺少ArrowProjectileComponent")

## 配置带伤害衰减的DamageComponent
func configureDamageComponentWithReduction(arrow: Entity):
	var arrowDamageComponent: DamageComponent = arrow.components.get(&"DamageComponent")
	if arrowDamageComponent:
		arrowDamageComponent.initiatorEntity = parentEntity
		# 应用伤害衰减
		var reducedDamage = baseDamage * (1.0 - dualShotDamageReduction / 100.0)
		arrowDamageComponent.damageOnCollision = int(reducedDamage)
		arrowDamageComponent.removeEntityOnApplyingDamage = true
		arrowDamageComponent.friendlyFire = false

		if debugBow:
			printLog("BowAttackComponent: 并排箭矢DamageComponent配置完成，伤害: " + str(reducedDamage))
	else:
		printWarning("BowAttackComponent: 箭矢缺少DamageComponent")

## 配置带角度偏移的ArrowProjectileComponent
func configureProjectileComponentWithOffset(arrow: Entity, targetPosition: Vector2, angleOffset: float):
	var arrowProjectileComponent: ArrowProjectileComponent = arrow.components.get(&"ArrowProjectileComponent")
	if arrowProjectileComponent:
		var baseDirection = getShootDirection(targetPosition)
		# 应用角度偏移（如果为0则直接使用基础方向）
		var finalDirection = baseDirection
		if angleOffset != 0.0:
			finalDirection = baseDirection.rotated(angleOffset)

		arrowProjectileComponent.launch(finalDirection, arrowSpeed, range)
		arrow.rotation = finalDirection.angle()

		if debugBow:
			printLog("BowAttackComponent: 箭矢ArrowProjectileComponent配置完成")
			printLog("  - 基础方向: " + str(baseDirection))
			if angleOffset != 0.0:
				printLog("  - 偏移角度: " + str(rad_to_deg(angleOffset)) + " 度")
				printLog("  - 最终方向: " + str(finalDirection))
			else:
				printLog("  - 无角度偏移，使用基础方向")
	else:
		printWarning("BowAttackComponent: 箭矢缺少ArrowProjectileComponent")

## 复制派系信息到箭矢
func copyFactionToArrow(arrow: Entity):
	var arrowFactionComponent: FactionComponent = arrow.components.get(&"FactionComponent")

	if factionComponent and arrowFactionComponent:
		arrowFactionComponent.factions = factionComponent.factions
		if debugBow:
			printLog("BowAttackComponent: 派系信息已复制")

## 获取射击方向（朝向鼠标或指定坐标）
func getShootDirection(targetPosition: Vector2 = Vector2.ZERO) -> Vector2:
	var emitterPosition = getRealtimeEmitterPosition()
	var direction: Vector2

	# 如果提供了目标坐标，使用目标坐标；否则使用鼠标位置
	if targetPosition != Vector2.ZERO:
		direction = (targetPosition - emitterPosition).normalized()
		if debugBow:
			printLog("BowAttackComponent: 射击方向计算（目标坐标）")
			printLog("  - 目标位置: " + str(targetPosition))
	else:
		var mouse_pos = parentEntity.get_global_mouse_position()
		direction = (mouse_pos - emitterPosition).normalized()
		if debugBow:
			printLog("BowAttackComponent: 射击方向计算（鼠标位置）")
			printLog("  - 鼠标位置: " + str(mouse_pos))

	# 处理边界情况
	if direction.length() < 0.1:
		direction = Vector2.RIGHT
		if debugBow:
			printWarning("BowAttackComponent: 目标位置过近，使用默认方向")

	if debugBow:
		printLog("  - 实时发射器位置: " + str(emitterPosition))
		printLog("  - 射击方向: " + str(direction))

	return direction

## 获取箭矢发射点
func getArrowEmitter() -> Node2D:
	if arrowEmitter:
		return arrowEmitter
	else:
		return parentEntity  # 使用父实体作为发射点

## 获取实时发射器位置 - 确保移动时位置正确
func getRealtimeEmitterPosition() -> Vector2:
	if arrowEmitter:
		return arrowEmitter.global_position
	else:
		return parentEntity.global_position

## 获取箭矢添加的目标父节点
func getTargetParent() -> Node:
	if arrowParentOverride:
		return arrowParentOverride
	else:
		# 默认添加到当前场景的根节点
		return get_tree().current_scene

## 验证组件配置是否正确
func validateConfiguration() -> bool:
	var isValid = true

	if not arrowEntity:
		printError("BowAttackComponent: arrowEntity未配置")
		isValid = false

	if baseDamage <= 0:
		printWarning("BowAttackComponent: baseDamage应该大于0")

	if range <= 0:
		printWarning("BowAttackComponent: range应该大于0")

	if arrowSpeed <= 0:
		printWarning("BowAttackComponent: arrowSpeed应该大于0")

	return isValid

## 验证自动射击配置
func validateAutoShootingConfig() -> bool:
	var isValid = true

	if autoDetectionRange <= 0:
		printWarning("BowAttackComponent: autoDetectionRange应该大于0")
		isValid = false

	if autoScanInterval <= 0:
		printWarning("BowAttackComponent: autoScanInterval应该大于0")
		isValid = false

	if enemyGroup.is_empty():
		printWarning("BowAttackComponent: enemyGroup不能为空")
		isValid = false

	if autoShootCooldown <= 0:
		printWarning("BowAttackComponent: autoShootCooldown应该大于0")
		isValid = false

	return isValid

## 获取自动射击调试信息
func getAutoShootingDebugInfo() -> Dictionary:
	return {
		"enableAutoShooting": enableAutoShooting,
		"autoDetectionRange": autoDetectionRange,
		"autoScanInterval": autoScanInterval,
		"enemyGroup": enemyGroup,
		"autoShootCooldown": autoShootCooldown,
		"currentAutoTarget": currentTarget.name if currentTarget else "无",
		"detectedEnemiesCount": detectedEnemies.size(),
		"lastScanTime": lastScanTime,
		"autoShootTimer": autoShootTimer
	}

## 获取组件调试信息
func getDebugInfo() -> Dictionary:
	var info = {
				   "isEnabled": isEnabled,
				   "isPlayerControlled": isPlayerControlled,
				   "hasCooldownCompleted": hasCooldownCompleted,
				   "baseDamage": baseDamage,
				   "cooldown": cooldown,
				   "range": range,
				   "arrowSpeed": arrowSpeed,
				   "arrowEntity": arrowEntity,
				   "factionComponent": factionComponent != null,
				   "enableDualShot": enableDualShot
			   }

	# 如果启用了并排发射，添加相关信息
	if enableDualShot:
		info["dualShotCount"] = dualShotCount
		info["dualShotSpacing"] = dualShotSpacing
		info["dualShotDamageReduction"] = dualShotDamageReduction
		info["dualShotReducedDamage"] = baseDamage * (1.0 - dualShotDamageReduction / 100.0)
		info["dualShotTotalDamage"] = info["dualShotReducedDamage"] * dualShotCount

	# 如果启用了自动射击，添加相关信息
	if enableAutoShooting:
		info.merge(getAutoShootingDebugInfo())

	return info

## 显示调试信息
func showDebugInfo():
	if not debugBow:
		return

	var info = getDebugInfo()
	print("=== BowAttackComponent Debug Info ===")
	for key in info:
		print(key + ": " + str(info[key]))
	print("=====================================")

#region 模式控制方法

## 获取当前射击模式
func getShootingMode() -> String:
	return "自动射击" if enableAutoShooting else "手动射击"

## 检查是否应该处理输入
func shouldProcessInput() -> bool:
	return not enableAutoShooting and isPlayerControlled and isEnabled

## 检查是否可以射击
func canShoot() -> bool:
	if not isEnabled:
		return false

	if not hasCooldownCompleted:
		return false

	if enableAutoShooting:
		return hasValidAutoTarget()
	else:
		return isPlayerControlled

## 检查是否有有效的自动目标
func hasValidAutoTarget() -> bool:
	return currentTarget != null and is_instance_valid(currentTarget)

## 清除自动目标和相关状态
func clearAutoTargetState() -> void:
	currentTarget = null
	detectedEnemies.clear()
	lastScanTime = 0.0
	# 注意：不再重置自动射击计时器，让冷却时间独立于目标状态
	# autoShootTimer = 0.0  # 已移除

	if debugBow:
		printLog("BowAttackComponent: 自动目标状态已清除")

## 重置所有射击相关计时器
func resetShootingTimers() -> void:
	lastScanTime = 0.0
# 注意：不再重置自动射击计时器，让冷却时间独立于模式切换
# autoShootTimer = 0.0  # 已移除

## 手动重置自动射击冷却时间（仅在需要时调用）
func resetAutoShootCooldown() -> void:
	autoShootTimer = 0.0
	if debugBow:
		printLog("BowAttackComponent: 手动重置自动射击冷却时间")

## 设置自动射击模式（主要用于运行时切换测试）
func setAutoShootingMode(enabled: bool) -> void:
	if enableAutoShooting == enabled:
		return

	enableAutoShooting = enabled

	if enabled:
		# 切换到自动模式
		resetShootingTimers()
		if debugBow:
			printLog("BowAttackComponent: 切换到自动射击模式")
	else:
		# 切换到手动模式
		clearAutoTargetState()
		if debugBow:
			printLog("BowAttackComponent: 切换到手动射击模式")

## 获取模式状态信息
func getModeStatus() -> Dictionary:
	return {
		"mode": getShootingMode(),
		"enableAutoShooting": enableAutoShooting,
		"canShoot": canShoot(),
		"shouldProcessInput": shouldProcessInput(),
		"hasValidTarget": hasValidAutoTarget(),
		"currentTarget": currentTarget.name if currentTarget else "无"
	}

#endregion

#region 敌人检测逻辑（完全参考EnemyDetectionComponent设计）

## 处理敌人检测逻辑（参考EnemyDetectionComponent.handleEnemyDetection）
func handleEnemyDetection() -> void:
	# 直接用工具类查找范围内的敌人
	detectedEnemies = FindInGroupUtils.get_entities_in_group_within_range(enemyGroup, getRealtimeEmitterPosition(), autoDetectionRange)
	# 获取最近的敌人
	var nearestEnemy = findNearestEnemy()
	if nearestEnemy:
		setTargetToEnemy(nearestEnemy)
	else:
		resetToDefaultDirection()
	if debugBow:
		printLog("BowAttackComponent: 范围内敌人: " + str(detectedEnemies.size()) + " 个")

## 检查Entity是否为敌人
func isEnemyEntity(entity: Entity) -> bool:
	"""检查节点是否挂载FactionComponent且factions包含enemies枚举值"""

	if not entity or entity == parentEntity:
		return false

	var entityFactionComponent: FactionComponent = entity.components.get(&"FactionComponent")
	# 必须有FactionComponent
	if not entityFactionComponent:
		return false
	# 使用位操作检查是否包含enemies派系 (FactionComponent.Factions.enemies = 4)
	return entityFactionComponent.factions == enemyFactionValue

## 检查目标是否已死亡
func isTargetDead(target: Entity) -> bool:
	"""检查目标是否已死亡"""
	if not target or not is_instance_valid(target):
		return true  # 无效目标视为已死亡
	
	var healthComponent: HealthComponent = target.components.get(&"HealthComponent")
	if healthComponent and healthComponent.health.value <= 0:
		if debugBow:
			printLog("BowAttackComponent: 目标已死亡: " + str(target.name))
		return true
	
	return false

## 检查敌人是否在范围内（参考EnemyDetectionComponent.isEnemyInRange）
func isEnemyInRange(enemy: Node) -> bool:
	"""检查敌人是否在范围内"""
	if not enemy or not is_instance_valid(enemy):
		return false

	# 确保是Entity类型
	if not enemy is Entity:
		return false

	var distance = getRealtimeEmitterPosition().distance_to(enemy.global_position)
	var inRange = distance <= autoDetectionRange
	
	# 检查目标是否已死亡
	if inRange and isTargetDead(enemy as Entity):
		return false

	return inRange

## 从检测到的敌人中找到最近的（参考EnemyDetectionComponent.findNearestEnemy）
func findNearestEnemy() -> Entity:
	"""从检测到的敌人中找到最近的"""
	if detectedEnemies.is_empty():
		return null

	var nearestEnemy: Entity = null
	var shortestDistance = INF
	var shooterPosition = getRealtimeEmitterPosition()

	for enemy in detectedEnemies:
		# 跳过已死亡的敌人
		if isTargetDead(enemy):
			continue
			
		var distance = shooterPosition.distance_to(enemy.global_position)
		if distance < shortestDistance:
			shortestDistance = distance
			nearestEnemy = enemy

	return nearestEnemy

## 设置敌人为目标（参考EnemyDetectionComponent.setTargetToEnemy）
func setTargetToEnemy(enemy: Entity) -> void:
	"""设置敌人为目标"""
	if not enemy or enemy == currentTarget:
		return

	var oldTarget = currentTarget
	currentTarget = enemy

	# 注意：不再重置自动射击计时器，让冷却时间独立于目标选择
	# autoShootTimer = 0.0  # 已移除

	# 发送信号
	if not oldTarget:
		target_detected.emit(enemy)
		if debugBow:
			printLog("BowAttackComponent: 目标检测到: " + str(enemy.name))
	elif oldTarget != enemy:
		target_changed.emit(oldTarget, enemy)
		if debugBow:
			printLog("BowAttackComponent: 目标切换从 " + str(oldTarget.name) + " 到 " + str(enemy.name))

## 重置到默认状态（参考EnemyDetectionComponent.resetToDefaultDirection）
func resetToDefaultDirection() -> void:
	"""重置到默认状态（无目标）"""
	if currentTarget:
		var oldTarget = currentTarget
		currentTarget = null

		# 发送目标丢失信号
		target_lost.emit()
		if debugBow:
			printLog("BowAttackComponent: 目标丢失: " + str(oldTarget.name))

# 注意：不再重置射击状态，让冷却时间独立于目标状态
# autoShootTimer = 0.0  # 已移除

## 自动射击更新（简化版本）
func updateAutoShooting(delta: float):
	if not enableAutoShooting or not isEnabled:
		return
	# 更新计时器
	autoShootTimer += delta

	#if debugBow:
	#print("BowAttackComponent: 自动射击计时器更新 - 当前: " + str(autoShootTimer) + " / 冷却: " + str(autoShootCooldown))

	# 检查是否可以射击 - 简化条件，只使用自动射击冷却
	if currentTarget and autoShootTimer >= autoShootCooldown:
		#if debugBow:
		#print("BowAttackComponent: 触发自动射击")
		performAutoShoot()

## 执行自动射击
func performAutoShoot():
	if not currentTarget:
		return
	
	# 检查目标是否已死亡
	if isTargetDead(currentTarget):
		if debugBow:
			printLog("BowAttackComponent: 目标已死亡，跳过自动射击")
		# 清除死亡目标并重新检测
		currentTarget = null
		handleEnemyDetection()
		
		# 如果重新检测到新目标，立即尝试射击（不重置计时器）
		if currentTarget and not isTargetDead(currentTarget):
			if debugBow:
				printLog("BowAttackComponent: 检测到新目标，立即尝试射击")
			# 递归调用，但这次应该能成功射击
			performAutoShoot()
		return
	
	# 只对玩家阵营生效
	if isPlayerFaction():
		if parentEntity and parentEntity.has_method("get_velocity"):
			var vel = parentEntity.get_velocity()
			if vel != Vector2.ZERO:
				if debugBow:
					print("BowAttackComponent: 玩家移动中，禁止自动射击")
				return
		elif parentEntity and parentEntity.has_variable("velocity"):
			if parentEntity.velocity != Vector2.ZERO:
				if debugBow:
					print("BowAttackComponent: 玩家移动中，禁止自动射击")
				return
	# 快速验证目标仍然有效
	if not isEnemyInRange(currentTarget):
		return
	var targetPosition = currentTarget.global_position
	request_attack(targetPosition)
	
	# 成功发起攻击后重置计时器
	autoShootTimer = 0.0
	if debugBow:
		printLog("BowAttackComponent: 自动射击成功，重置计时器")
# var arrow = fireAtTarget(targetPosition)
# if arrow:
# 	autoShootTimer = 0.0
# 	if debugBow:
# 		printLog("BowAttackComponent: 自动射击目标 " + currentTarget.name)
# else:
# 	if debugBow:
# 		printWarning("BowAttackComponent: 自动射击失败")

## 当enableAutoShooting改变时调用
func _on_enable_auto_shooting_changed():
	set_process(enableAutoShooting)

	if not enableAutoShooting:
		# 关闭自动射击时清理状态
		currentTarget = null
		detectedEnemies.clear()
		# 注意：不再重置自动射击计时器，让冷却时间独立于模式切换
		# autoShootTimer = 0.0  # 已移除

		if debugBow:
			printLog("BowAttackComponent: 自动射击已禁用，清理状态")
	else:
		if debugBow:
			printLog("BowAttackComponent: 自动射击已启用")

#endregion

func isPlayerFaction() -> bool:
	# 假设玩家派系为2（可根据实际派系调整）
	return factionComponent and factionComponent.factions == 2

func _on_player_attack_animation_finished(target_position: Vector2) -> void:
	print("[BowAttackComponent] 收到玩家攻击动画完成信号，准备攻击目标：", target_position)
	
	# 检查当前目标是否已死亡
	if currentTarget and isTargetDead(currentTarget):
		if debugBow:
			printLog("BowAttackComponent: 动画完成时发现目标已死亡，跳过射击")
		currentTarget = null
		# 不重置计时器，让系统继续寻找新目标
		return
	
	var arrow = fireAtTarget(target_position)
	if arrow:
		autoShootTimer = 0.0
		if debugBow:
			printLog("BowAttackComponent: 动画射击成功，重置计时器")
	else:
		if debugBow:
			printWarning("BowAttackComponent: 动画射击失败")
	print("[BowAttackComponent] 已执行fireAtTarget")

func request_attack(target_position: Vector2) -> void:
	print("[BowAttackComponent] 请求攻击，先发射before_attack信号")
	emit_signal("before_attack", target_position)

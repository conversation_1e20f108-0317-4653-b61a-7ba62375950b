[gd_scene load_steps=2 format=3 uid="uid://3paq4i75vagc"]

[ext_resource type="Script" uid="uid://c1esp18eyiq7e" path="res://Game/tt/Components/Lifebar/LifeBarTest.gd" id="1_test_script"]

[node name="LifeBarTest" type="Node"]
script = ExtResource("1_test_script")

[node name="Camera2D" type="Camera2D" parent="."]
position = Vector2(400, 300)

[node name="UI" type="CanvasLayer" parent="."]

[node name="Instructions" type="Label" parent="UI"]
offset_left = 10.0
offset_top = 10.0
offset_right = 500.0
offset_bottom = 150.0
text = "血条组件血量变化数值显示功能测试

按键说明：
1 - 减血10点
2 - 加血15点  
3 - 切换角色类型（玩家/敌人）
4 - 切换数值显示开关
5 - 显示性能统计

测试会自动运行，也可以手动按键测试"

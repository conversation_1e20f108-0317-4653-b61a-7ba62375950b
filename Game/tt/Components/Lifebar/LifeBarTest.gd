## 血条组件血量变化数值显示功能测试脚本
## 用于验证新实现的功能是否正常工作

extends Node

var test_entity: Node
var health_component: HealthComponent
var lifebar_component: LifeBarComponent

func _ready():
	print("=== 血条组件血量变化数值显示功能测试开始 ===")
	setup_test_entity()
	run_tests()

func setup_test_entity():
	# 创建测试实体
	test_entity = Node.new()
	test_entity.name = "TestEntity"
	add_child(test_entity)
	
	# 创建健康组件
	health_component = HealthComponent.new()
	var health_stat = Stat.new()
	health_stat.max = 100
	health_stat.value = 100
	health_component.health = health_stat
	test_entity.add_child(health_component)
	
	# 创建血条组件
	var lifebar_scene = preload("res://Game/tt/Components/Lifebar/LifeBarComponent.tscn")
	lifebar_component = lifebar_scene.instantiate()
	lifebar_component.show_damage_numbers = true
	lifebar_component.debug_damage_display = true
	test_entity.add_child(lifebar_component)
	
	print("测试实体设置完成")

func run_tests():
	print("\n=== 开始功能测试 ===")
	
	# 等待组件初始化
	await get_tree().process_frame
	await get_tree().process_frame
	
	# 测试1：血量减少显示
	print("\n测试1：血量减少显示")
	test_damage_display()
	
	# 等待动画完成
	await get_tree().create_timer(2.0).timeout
	
	# 测试2：血量增加显示
	print("\n测试2：血量增加显示")
	test_heal_display()
	
	# 等待动画完成
	await get_tree().create_timer(2.0).timeout
	
	# 测试3：角色类型动画
	print("\n测试3：玩家角色动画")
	test_player_character_animation()
	
	# 等待动画完成
	await get_tree().create_timer(2.0).timeout
	
	# 测试4：快速连续变化
	print("\n测试4：快速连续变化")
	test_rapid_changes()
	
	print("\n=== 测试完成 ===")

func test_damage_display():
	print("模拟血量减少30点")
	health_component.damage(30)
	print("当前血量：", health_component.health.value)

func test_heal_display():
	print("模拟血量增加20点")
	health_component.health.value += 20
	health_component.health.changed.emit()
	print("当前血量：", health_component.health.value)

func test_player_character_animation():
	print("设置为玩家角色并测试动画")
	lifebar_component.is_player_character = true
	
	# 测试玩家减血动画
	print("玩家减血动画")
	health_component.damage(15)
	
	await get_tree().create_timer(1.5).timeout
	
	# 测试玩家加血动画
	print("玩家加血动画")
	health_component.health.value += 25
	health_component.health.changed.emit()

func test_rapid_changes():
	print("测试快速连续变化")
	for i in range(5):
		health_component.damage(5)
		await get_tree().process_frame

func _input(event):
	if event is InputEventKey and event.pressed:
		match event.keycode:
			KEY_1:
				print("手动测试：减血10点")
				health_component.damage(10)
			KEY_2:
				print("手动测试：加血15点")
				health_component.health.value += 15
				health_component.health.changed.emit()
			KEY_3:
				print("切换角色类型")
				lifebar_component.is_player_character = !lifebar_component.is_player_character
				print("当前角色类型：", "玩家" if lifebar_component.is_player_character else "敌人")
			KEY_4:
				print("切换数值显示")
				lifebar_component.show_damage_numbers = !lifebar_component.show_damage_numbers
				print("数值显示：", "开启" if lifebar_component.show_damage_numbers else "关闭")
			KEY_5:
				print("性能统计：", lifebar_component._get_performance_stats())

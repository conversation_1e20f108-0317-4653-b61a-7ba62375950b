## Stores the entity's lifebar and manages display for health visualization.
## Requires a HealthComponent on the same entity to display health values.
## Automatically follows the entity's position and updates when health changes.

class_name LifeBarComponent
extends Component

#region Exported Properties

@export var offset: Vector2 = Vector2(0, -32)
@export var bar_width: int = 55
@export var bar_height: int = 7
@export var border_width: int = 1
@export var divider_width: int = 0
@export var animation_duration: float = 0.25
@export var auto_hide_when_full: bool = false
@export var hide_delay: float = 3.0
@export var show_health_value: bool = false
@export var font_size: int = 12

# Color definitions
@export var progress_color: Color = Color("#D35A51")
@export var background_color: Color = Color("#333333")

#region Damage Number Display Properties
@export_group("Damage Numbers")
@export var show_damage_numbers: bool = true
@export var damage_text_font_size: int = 12
@export var damage_color: Color = Color("#FF4444")
@export var heal_color: Color = Color("#44FF44")
@export var is_player_character: bool = false
@export var debug_damage_display: bool = false
#endregion

#endregion

#region Internal Variables

var health_component: HealthComponent
@onready var lifebar_container: Control = $LifeBarContainer
@onready var background: Control = $LifeBarContainer/Background
@onready var progress_bar: ProgressBar = $LifeBarContainer/ProgressBar
@onready var health_label: BitmapNumberDisplay = $LifeBarContainer/HealthNumberContainer/HealthLabel
@onready var hide_timer: Timer = $HideTimer
var tween: Tween

# Divider nodes (created dynamically)
var divider_nodes: Array[Panel] = []

#region Damage Number Internal Variables
@onready var damage_number_display: BitmapNumberDisplay = $LifeBarContainer/DamageNumberDisplay
var damage_animation_tween: Tween
var is_damage_animation_playing: bool = false
var damage_display_base_position: Vector2
var damage_display_queue: Array[int] = []
var max_queue_size: int = 3
#endregion

#endregion

#region Component Lifecycle

func _ready() -> void:
	printDebug("LifeBarComponent: parentEntity = " + str(parentEntity))
	find_health_component()
	if health_component:
		printDebug("LifeBarComponent: Initial health - value: " + str(health_component.health.value) + ", max: " + str(health_component.health.max))
		validate_configuration()
		setup_ui()
		setup_damage_display()  # 新增
		connect_signals()
		update_progress(false)
		show_lifebar()  # 确保血条在初始化时显示
		update_health_label()  # 初始化血量数值显示状态
	else:
		printError("LifeBarComponent: HealthComponent not found on entity")

func _process(_delta: float) -> void:
	if lifebar_container and parentEntity:
		update_position()

func _exit_tree() -> void:
	cleanup()

#endregion

#region Health Component Management

func find_health_component() -> void:
	if not parentEntity:
		printWarning("find_health_component: parentEntity is null")
		return
	
	health_component = parentEntity.getComponent(HealthComponent)
	if not health_component:
		# Try to find in parent node
		var parent: Node = parentEntity.get_parent()
		if parent and parent.has_method("getComponent"):
			health_component = parent.getComponent(HealthComponent)
	
	# 如果仍然找不到，尝试直接查找节点
	if not health_component:
		health_component = parentEntity.findFirstChildOfClass(HealthComponent)

func connect_signals() -> void:
	if health_component:
		# 现有连接保持不变
		health_component.healthDidDecrease.connect(on_health_changed)
		health_component.healthDidIncrease.connect(on_health_changed)
		health_component.healthDidZero.connect(on_health_zero)

		# 新增：专门处理数值显示的信号连接
		health_component.healthDidDecrease.connect(on_health_decreased_for_display)
		health_component.healthDidIncrease.connect(on_health_increased_for_display)

func on_health_changed(_difference: int) -> void:
	update_progress(true)
	show_lifebar()
	
func on_health_zero() -> void:
	hide_lifebar()

func on_health_decreased_for_display(difference: int) -> void:
	if show_damage_numbers:
		printDebug("LifeBarComponent: 血量减少 " + str(difference) + "，传递数值 " + str(-difference))
		show_damage_number(difference)

func on_health_increased_for_display(difference: int) -> void:
	if show_damage_numbers:
		printDebug("LifeBarComponent: 血量增加 " + str(difference) + "，传递数值 " + str(difference))
		show_damage_number(-difference)

## 强制立即更新血条显示（外部调用接口）
## 用于血量资源被替换后立即更新显示，避免等待定时器检查
func force_update_display() -> void:
	if not health_component:
		return
	# 立即更新血条显示
	update_progress(false)

#endregion

#region UI Setup

func setup_ui() -> void:
	# Configure nodes that are already created in the scene
	configure_lifebar_container()
	configure_background()
	configure_progress_bar()
	configure_health_label()
	configure_hide_timer()
	position_lifebar()

func setup_damage_display() -> void:
	if not damage_number_display:
		printError("LifeBarComponent: DamageNumberDisplay not found")
		return

	# 设置基础位置（血条中心上方）
	damage_display_base_position = Vector2(
		0.0,
		-damage_text_font_size - 5  # 字体大小 + 5像素间距
	)

	# 初始状态设置
	damage_number_display.visible = false
	damage_number_display.scale = Vector2.ZERO
	damage_number_display.position = damage_display_base_position

	# 应用字体大小和投影效果
	configure_damage_display_style()

func configure_damage_display_style() -> void:
	if not damage_number_display:
		return

	# 设置字体大小（通过缩放因子）
	var scale_ratio: float = damage_text_font_size / 12.0  # 12是默认字体大小
	damage_number_display.scale_factor = scale_ratio

	# 设置尺寸预设为战斗界面
	damage_number_display.size_preset = BitmapNumberDisplay.SizePreset.BATTLE_UI

func _validate_property(property: Dictionary) -> void:
	# 当属性在编辑器中改变时调用
	match property.name:
		"damage_text_font_size":
			if damage_number_display:
				configure_damage_display_style()
		"damage_color", "heal_color":
			# 颜色变化会在下次显示时生效
			pass
		"show_damage_numbers":
			if not show_damage_numbers and damage_number_display:
				damage_number_display.visible = false

# 公共接口：运行时更新配置
func update_damage_display_config(
	new_font_size: int = -1,
	damage_col: Color = Color.TRANSPARENT,
	heal_col: Color = Color.TRANSPARENT,
	show_numbers: bool = true
) -> void:
	if new_font_size > 0:
		damage_text_font_size = new_font_size
	if damage_col != Color.TRANSPARENT:
		damage_color = damage_col
	if heal_col != Color.TRANSPARENT:
		heal_color = heal_col
	show_damage_numbers = show_numbers

	configure_damage_display_style()

func configure_lifebar_container() -> void:
	var container_width: int = bar_width + (border_width * 2)
	var container_height: int = bar_height + (border_width * 2) + 15  # +15 for label (10px height + 5px spacing)
	lifebar_container.size = Vector2(container_width, container_height)
	# Container will be positioned dynamically in update_position()

func configure_background() -> void:
	background.size = Vector2(bar_width + (border_width * 2), bar_height + (border_width * 2))

	# 创建圆角背景样式
	var background_style: StyleBoxFlat = StyleBoxFlat.new()
	background_style.bg_color = background_color
	var total_height: int = bar_height + (border_width * 2)
	var corner_radius: int = int(total_height / 2.0)
	background_style.corner_radius_top_left = corner_radius
	background_style.corner_radius_top_right = corner_radius
	background_style.corner_radius_bottom_left = corner_radius
	background_style.corner_radius_bottom_right = corner_radius

	# 设置Panel样式
	(background as Panel).add_theme_stylebox_override("panel", background_style)

func configure_progress_bar() -> void:
	# 计算进度条的实际尺寸
	var progress_width: int = bar_width
	var progress_height: int = bar_height
	progress_bar.size = Vector2(progress_width, progress_height)
	progress_bar.position = Vector2(border_width, border_width)  # 居中显示
	progress_bar.min_value = 0
	progress_bar.max_value = 100
	progress_bar.value = 100
	progress_bar.show_percentage = false

	# 创建圆角进度条样式
	var progress_style: StyleBoxFlat = StyleBoxFlat.new()
	progress_style.bg_color = progress_color
	var progress_corner_radius: int = int(progress_height / 2.0)
	progress_style.corner_radius_top_left = progress_corner_radius
	progress_style.corner_radius_top_right = progress_corner_radius
	progress_style.corner_radius_bottom_left = progress_corner_radius
	progress_style.corner_radius_bottom_right = progress_corner_radius

	# 设置进度条样式
	progress_bar.add_theme_stylebox_override("fill", progress_style)

	# 创建透明的背景样式（进度条未填充部分）
	var progress_bg_style: StyleBoxFlat = StyleBoxFlat.new()
	progress_bg_style.bg_color = Color.TRANSPARENT
	progress_bg_style.corner_radius_top_left = progress_corner_radius
	progress_bg_style.corner_radius_top_right = progress_corner_radius
	progress_bg_style.corner_radius_bottom_left = progress_corner_radius
	progress_bg_style.corner_radius_bottom_right = progress_corner_radius
	progress_bar.add_theme_stylebox_override("background", progress_bg_style)

	# 配置分割线
	configure_dividers()

func configure_health_label() -> void:
	if not health_label:
		return

	# 设置位图数字显示的尺寸预设为战斗界面
	health_label.size_preset = BitmapNumberDisplay.SizePreset.BATTLE_UI

	# 设置缩放因子来控制数字大小（相当于之前的字体大小控制）
	var scale_ratio: float = font_size / 12.0  # 12是默认字体大小
	health_label.scale_factor = scale_ratio

	# 确保血量数字容器始终在最上层显示（高于分割线）
	var health_container: Control = health_label.get_parent() as Control  # HealthNumberContainer
	if health_container:
		health_container.z_index = 10  # 设置较高的z-index确保在分割线上方

	# BitmapNumberDisplay会自动居中对齐，不需要手动设置对齐方式

func configure_dividers() -> void:
	# 清理现有的分割线
	clear_dividers()

	# 如果分割线宽度为0，不创建分割线
	if divider_width <= 0:
		return

	# 计算分割线位置（25%, 50%, 75%）
	var progress_width: int = bar_width
	var progress_height: int = bar_height
	var divider_positions: Array[float] = [0.25, 0.5, 0.75]

	# 创建三条分割线
	for i in range(3):
		var divider: Panel = Panel.new()
		divider.name = "Divider" + str(i + 1)

		# 计算分割线位置
		var x_position: float = progress_width * divider_positions[i] - (divider_width / 2.0) + border_width
		var y_position: float = border_width

		# 设置分割线尺寸和位置
		divider.size = Vector2(divider_width, progress_height)
		divider.position = Vector2(x_position, y_position)

		# 创建分割线样式
		var divider_style: StyleBoxFlat = StyleBoxFlat.new()
		divider_style.bg_color = background_color
		divider.add_theme_stylebox_override("panel", divider_style)

		# 添加到容器并保存引用
		lifebar_container.add_child(divider)
		divider_nodes.append(divider)

func clear_dividers() -> void:
	# 移除现有的分割线节点
	for divider in divider_nodes:
		if divider and is_instance_valid(divider):
			divider.queue_free()
	divider_nodes.clear()

## 根据血量百分比更新分割线显示状态
func update_dividers_visibility(health_percentage: float) -> void:
	if divider_nodes.is_empty():
		printDebug("LifeBarComponent: 没有分割线节点，跳过可见性更新")
		return

	printDebug("LifeBarComponent: 更新分割线可见性，当前血量百分比: " + str(health_percentage * 100) + "%")

	# 分割线显示规则：
	# 第1条分割线(25%)：血量 > 25% 时显示
	# 第2条分割线(50%)：血量 > 50% 时显示
	# 第3条分割线(75%)：血量 > 75% 时显示
	var divider_thresholds: Array[float] = [0.25, 0.5, 0.75]

	for i in range(min(divider_nodes.size(), divider_thresholds.size())):
		var divider: Panel = divider_nodes[i]
		if divider and is_instance_valid(divider):
			# 当血量大于对应阈值时显示分割线
			divider.visible = health_percentage > divider_thresholds[i]

			if divider.visible:
				printDebug("LifeBarComponent: 显示分割线 " + str(i + 1) + " (阈值: " + str(divider_thresholds[i] * 100) + "%, 当前血量: " + str(health_percentage * 100) + "%)")
			else:
				printDebug("LifeBarComponent: 隐藏分割线 " + str(i + 1) + " (阈值: " + str(divider_thresholds[i] * 100) + "%, 当前血量: " + str(health_percentage * 100) + "%)")

func configure_hide_timer() -> void:
	hide_timer.wait_time = hide_delay
	hide_timer.one_shot = true
	hide_timer.timeout.connect(hide_lifebar)

func position_lifebar() -> void:
	if lifebar_container and parentEntity:
		update_position()

#endregion

#region Progress Management

func update_progress(animate: bool = true) -> void:
	if not health_component or not progress_bar:
		return
	
	var percentage: float = get_health_percentage()
	var target_value: float = percentage * 100
	
	if animate and tween:
		tween.kill()
	
	tween = create_tween()
	tween.set_ease(Tween.EASE_OUT)
	tween.set_trans(Tween.TRANS_CUBIC)
	
	if animate:
		tween.tween_property(progress_bar, "value", target_value, animation_duration)
		tween.parallel().tween_callback(func() -> void: update_progress_color(percentage))
	else:
		progress_bar.value = target_value
		update_progress_color(percentage)
	
	update_health_label()
	update_dividers_visibility(percentage)

func get_health_percentage() -> float:
	if not health_component or not health_component.health:
		return 0.0
	return health_component.health.percentage / 100.0

func is_health_full() -> bool:
	return get_health_percentage() >= 1.0

func update_health_label() -> void:
	if not health_component or not health_label:
		return

	var current_health: int = health_component.health.value
	var max_health: int = health_component.health.max

	# 调试信息：打印实际获取的血量值
	printDebug("LifeBarComponent: current_health=" + str(current_health) + ", max_health=" + str(max_health) + ", health_component=" + str(health_component))

	# 根据参数控制是否显示血量数值
	if show_health_value:
		health_label.set_number(current_health)  # 使用BitmapNumberDisplay的set_number方法
		health_label.visible = true
	else:
		health_label.visible = false

#endregion

#region Color Management

func get_health_color(_percentage: float) -> Color:
	# 始终使用指定的进度条颜色 #D35A51
	return progress_color

func update_progress_color(percentage: float) -> void:
	var color: Color = get_health_color(percentage)
	if progress_bar:
		# 更新进度条填充样式的颜色
		var progress_style: StyleBoxFlat = progress_bar.get_theme_stylebox("fill")
		if progress_style:
			progress_style.bg_color = color

#endregion

#region Position Tracking

func update_position() -> void:
	var screen_pos: Vector2 = get_entity_screen_position()
	if screen_pos != Vector2.INF:
		# printDebug("LifeBarComponent: Updating position to " + str(screen_pos))
		# Since lifebar_container is now a child of this component,
		# we need to move the entire component or reparent the container to a canvas layer
		move_container_to_screen_position(screen_pos + offset)
	else:
		printWarning("LifeBarComponent: Invalid screen position")

func move_container_to_screen_position(screen_pos: Vector2) -> void:
	# We need to reparent the container to a CanvasLayer for proper screen positioning
	if lifebar_container.get_parent() == self:
		# First time - move to canvas layer (deferred to avoid scene tree setup conflicts)
		call_deferred("_move_to_canvas_layer")

	# Set position - center the lifebar on the entity (accounting for health label height and border)
	var total_width: int = bar_width + (border_width * 2)
	var total_height: int = bar_height + (border_width * 2) + 15  # bar_height + border + label height (10px + 5px spacing)
	lifebar_container.global_position = screen_pos - Vector2(total_width / 2.0, total_height / 2.0)

func _move_to_canvas_layer() -> void:
	if not lifebar_container or lifebar_container.get_parent() != self:
		return

	var canvas_layer: CanvasLayer = null
	# Try to find existing CanvasLayer in the scene
	for child in get_tree().current_scene.get_children():
		if child is CanvasLayer and child.layer == 1:
			canvas_layer = child
			break

	if not canvas_layer:
		canvas_layer = CanvasLayer.new()
		canvas_layer.layer = 1
		get_tree().current_scene.add_child(canvas_layer)

	# Reparent to canvas layer
	remove_child(lifebar_container)
	canvas_layer.add_child(lifebar_container)

func get_entity_screen_position() -> Vector2:
	if not parentEntity:
		return Vector2.INF
	
	# Get entity's global position
	var world_pos: Vector2 = parentEntity.global_position
	
	# Convert to screen coordinates
	var camera: Camera2D = get_viewport().get_camera_2d()
	if camera:
		# Convert world position to screen position using viewport transform
		return get_viewport().get_canvas_transform() * world_pos
	else:
		return get_viewport().get_canvas_transform() * world_pos

#endregion

#region Display Control

func show_lifebar() -> void:
	if lifebar_container:
		lifebar_container.visible = true
		lifebar_container.modulate.a = 1.0
	
	# Stop hide timer
	if hide_timer:
		hide_timer.stop()

func hide_lifebar() -> void:
	if not lifebar_container:
		return
	
	var fade_tween: Tween = create_tween()
	fade_tween.tween_property(lifebar_container, "modulate:a", 0.0, 0.5)
	fade_tween.tween_callback(func() -> void: lifebar_container.visible = false)

#endregion

#region Configuration & Cleanup

func validate_configuration() -> void:
	# Validate dimensions
	bar_width = max(bar_width, 16)
	bar_height = max(bar_height, 4)

	# Validate animation duration
	animation_duration = clamp(animation_duration, 0.1, 2.0)

func cleanup() -> void:
	if tween:
		tween.kill()

	# Clean up damage animation resources
	if damage_animation_tween and damage_animation_tween.is_valid():
		damage_animation_tween.kill()

	damage_display_queue.clear()

	# Clean up dividers
	clear_dividers()

	# If container was moved to canvas layer, clean it up
	if lifebar_container and lifebar_container.get_parent() != self:
		lifebar_container.queue_free()

	# Disconnect signals
	if health_component:
		if health_component.healthDidDecrease.is_connected(on_health_changed):
			health_component.healthDidDecrease.disconnect(on_health_changed)
		if health_component.healthDidIncrease.is_connected(on_health_changed):
			health_component.healthDidIncrease.disconnect(on_health_changed)
		if health_component.healthDidZero.is_connected(on_health_zero):
			health_component.healthDidZero.disconnect(on_health_zero)
		# Disconnect damage display signals
		if health_component.healthDidDecrease.is_connected(on_health_decreased_for_display):
			health_component.healthDidDecrease.disconnect(on_health_decreased_for_display)
		if health_component.healthDidIncrease.is_connected(on_health_increased_for_display):
			health_component.healthDidIncrease.disconnect(on_health_increased_for_display)

#endregion

#region Damage Number Display Functions

func show_damage_number(value: int) -> void:
	# 参数验证
	if value == 0:
		if debug_damage_display:
			printDebug("LifeBarComponent: 忽略零值变化")
		return

	if not damage_number_display:
		printError("LifeBarComponent: DamageNumberDisplay节点未找到")
		return

	if not show_damage_numbers:
		if debug_damage_display:
			printDebug("LifeBarComponent: 数值显示已禁用")
		return

	if debug_damage_display:
		printDebug("LifeBarComponent: 显示数值变化 " + str(value))

	# 如果当前有动画在播放，加入队列
	if is_damage_animation_playing:
		if damage_display_queue.size() < max_queue_size:
			damage_display_queue.append(value)
		else:
			# 队列满时，累加数值
			damage_display_queue[-1] += value
		return

	# 直接播放动画
	configure_damage_display(value)
	play_damage_animation(value)

func configure_damage_display(value: int) -> void:
	if not damage_number_display:
		return

	# 设置数值（包含符号）
	var display_text: String = "+" + str(value) if value > 0 else "-" + str(value)
	damage_number_display.set_text(display_text)

	# 设置颜色
	var color: Color = heal_color if value > 0 else damage_color
	printDebug("LifeBarComponent: 设置颜色，数值=" + str(value) + "，颜色=" + ("绿色" if value > 0 else "红色"))
	damage_number_display.modulate = color

	# 重置位置和缩放
	damage_number_display.position = damage_display_base_position
	damage_number_display.scale = Vector2.ZERO
	damage_number_display.visible = true

func play_damage_animation(value: int) -> void:
	if not damage_number_display:
		printError("LifeBarComponent: 无法播放动画，显示节点不存在")
		return

	# 清理之前的动画
	if damage_animation_tween and damage_animation_tween.is_valid():
		damage_animation_tween.kill()

	is_damage_animation_playing = true

	# 根据角色类型和数值类型选择动画
	if is_player_character:
		if value > 0:
			damage_animation_tween = create_player_heal_animation()
		else:
			damage_animation_tween = create_player_damage_animation()
	else:
		# 敌人只有减血动画（增血使用相同动画）
		damage_animation_tween = create_enemy_damage_animation()

	# 动画完成回调
	damage_animation_tween.finished.connect(on_damage_animation_finished)

	if debug_damage_display:
		var animation_type: String = "敌人减血"
		if is_player_character:
			animation_type = "玩家加血" if value > 0 else "玩家减血"
		printDebug("LifeBarComponent: 播放动画类型 - " + animation_type)

func on_damage_animation_finished() -> void:
	is_damage_animation_playing = false
	if damage_number_display:
		damage_number_display.visible = false

	# 处理队列中的下一个数值
	if not damage_display_queue.is_empty():
		var next_value: int = damage_display_queue.pop_front()
		# 延迟一帧再播放下一个动画
		get_tree().process_frame.connect(
			func() -> void: show_damage_number(next_value),
			CONNECT_ONE_SHOT
		)

func create_enemy_damage_animation() -> Tween:
	var damage_tween: Tween = create_tween()
	damage_tween.set_parallel(true)  # 允许并行动画

	# 关键帧时间计算（基于30帧）
	var frame_3_time: float = 3.0 / 30.0   # 0.1秒
	var frame_5_time: float = 5.0 / 30.0   # 0.167秒
	var frame_15_time: float = 15.0 / 30.0 # 0.5秒
	var frame_20_time: float = 20.0 / 30.0 # 0.667秒

	# 位置动画序列
	damage_tween.tween_callback(
		func() -> void: set_damage_display_position(Vector2(0, 0))
	).set_delay(0.0)
	damage_tween.tween_callback(
		func() -> void: set_damage_display_position(Vector2(0, -10))
	).set_delay(frame_3_time)
	damage_tween.tween_callback(
		func() -> void: set_damage_display_position(Vector2(0, 0))
	).set_delay(frame_5_time)
	damage_tween.tween_callback(
		func() -> void: set_damage_display_position(Vector2(0, 0))
	).set_delay(frame_15_time)

	# 缩放动画序列
	damage_tween.tween_property(
		damage_number_display, "scale",
		Vector2(1, 1),      # 3帧：显示
		frame_3_time
	)
	damage_tween.tween_property(
		damage_number_display, "scale",
		Vector2(1, 1),      # 15帧：保持显示
		frame_15_time - frame_3_time
	)
	damage_tween.tween_property(
		damage_number_display, "scale",
		Vector2(0, 0),      # 20帧：消失
		frame_20_time - frame_15_time
	)

	return damage_tween

func set_damage_display_position(display_offset: Vector2) -> void:
	if damage_number_display:
		damage_number_display.position = damage_display_base_position + display_offset

func create_player_damage_animation() -> Tween:
	var damage_tween: Tween = create_tween()
	damage_tween.set_parallel(true)

	# 关键帧时间
	var frame_1_time: float = 1.0 / 30.0   # 0.033秒
	var frame_3_time: float = 3.0 / 30.0   # 0.1秒
	var frame_25_time: float = 25.0 / 30.0 # 0.833秒
	var frame_30_time: float = 30.0 / 30.0 # 1.0秒

	# 位置动画：0帧(0,0) → 1帧(15,-32) → 3帧(45,0) → 25帧(45,0) → 30帧(45,0)
	damage_tween.tween_callback(
		func() -> void: set_damage_display_position(Vector2(0, 0))
	).set_delay(0.0)
	damage_tween.tween_callback(
		func() -> void: set_damage_display_position(Vector2(15, -32))
	).set_delay(frame_1_time)
	damage_tween.tween_callback(
		func() -> void: set_damage_display_position(Vector2(45, 0))
	).set_delay(frame_3_time)
	damage_tween.tween_callback(
		func() -> void: set_damage_display_position(Vector2(45, 0))
	).set_delay(frame_25_time)

	# 缩放动画：0帧(0,0) → 1帧(0,0) → 3帧(1,1) → 25帧(1,1) → 30帧(0,0)
	damage_tween.tween_property(
		damage_number_display, "scale",
		Vector2(0, 0),
		frame_1_time
	)
	damage_tween.tween_property(
		damage_number_display, "scale",
		Vector2(1, 1),
		frame_3_time - frame_1_time
	)
	damage_tween.tween_property(
		damage_number_display, "scale",
		Vector2(1, 1),
		frame_25_time - frame_3_time
	)
	damage_tween.tween_property(
		damage_number_display, "scale",
		Vector2(0, 0),
		frame_30_time - frame_25_time
	)

	return damage_tween

func create_player_heal_animation() -> Tween:
	var heal_tween: Tween = create_tween()
	heal_tween.set_parallel(true)

	# 关键帧时间
	var frame_3_time: float = 3.0 / 30.0   # 0.1秒
	var frame_4_time: float = 4.0 / 30.0   # 0.133秒
	var frame_5_time: float = 5.0 / 30.0   # 0.167秒
	var frame_25_time: float = 25.0 / 30.0 # 0.833秒
	var frame_30_time: float = 30.0 / 30.0 # 1.0秒

	# 位置动画：0帧(-45,-45) → 3帧(0,0) → 25帧(0,0) → 30帧(0,45)
	heal_tween.tween_callback(
		func() -> void: set_damage_display_position(Vector2(-45, -45))
	).set_delay(0.0)
	heal_tween.tween_callback(
		func() -> void: set_damage_display_position(Vector2(0, 0))
	).set_delay(frame_3_time)
	heal_tween.tween_callback(
		func() -> void: set_damage_display_position(Vector2(0, 0))
	).set_delay(frame_25_time)
	heal_tween.tween_callback(
		func() -> void: set_damage_display_position(Vector2(0, 45))
	).set_delay(frame_30_time)

	# 缩放动画：0帧(0,0) → 3帧(1,1) → 4帧(2,2) → 5帧(1,1) → 25帧(1,1) → 30帧(0,0)
	heal_tween.tween_property(
		damage_number_display, "scale",
		Vector2(1, 1),
		frame_3_time
	)
	heal_tween.tween_property(
		damage_number_display, "scale",
		Vector2(2, 2),
		frame_4_time - frame_3_time
	)
	heal_tween.tween_property(
		damage_number_display, "scale",
		Vector2(1, 1),
		frame_5_time - frame_4_time
	)
	heal_tween.tween_property(
		damage_number_display, "scale",
		Vector2(1, 1),
		frame_25_time - frame_5_time
	)
	heal_tween.tween_property(
		damage_number_display, "scale",
		Vector2(0, 0),
		frame_30_time - frame_25_time
	)

	return heal_tween

# 性能监控
func _get_performance_stats() -> Dictionary:
	return {
		"queue_size": damage_display_queue.size(),
		"is_animating": is_damage_animation_playing,
		"max_queue_size": max_queue_size
	}

#endregion

#region Required Components

func getRequiredComponents() -> Array[Script]:
	return [HealthComponent]

#endregion

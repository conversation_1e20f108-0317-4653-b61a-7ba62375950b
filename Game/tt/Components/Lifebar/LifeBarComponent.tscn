[gd_scene load_steps=3 format=3 uid="uid://dgt2shqy7tmrg"]

[ext_resource type="Script" uid="uid://cbtahv72qnt80" path="res://Game/tt/Components/Lifebar/LifeBarComponent.gd" id="1_lifebar"]
[ext_resource type="PackedScene" uid="uid://bqxvhqjxqhqxh" path="res://Game/tt/Components/UI/Components/BitmapNumberDisplay.tscn" id="2_bitmap_display"]

[node name="LifeBarComponent" type="Node" groups=["components"]]
script = ExtResource("1_lifebar")
progress_color = Color(0.827451, 0.352941, 0.317647, 1)

[node name="LifeBarContainer" type="Control" parent="."]
layout_mode = 3
anchors_preset = 0
offset_right = 57.0
offset_bottom = 24.0
grow_horizontal = 2
grow_vertical = 2

[node name="Background" type="Panel" parent="LifeBarContainer"]
layout_mode = 1
offset_right = 57.0
offset_bottom = 9.0
grow_horizontal = 2
grow_vertical = 2

[node name="ProgressBar" type="ProgressBar" parent="LifeBarContainer"]
layout_mode = 1
offset_left = 1.0
offset_top = 1.0
offset_right = 56.0
offset_bottom = 8.0
grow_horizontal = 2
grow_vertical = 2
value = 100.0
show_percentage = false

[node name="HealthNumberContainer" type="Control" parent="LifeBarContainer"]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -27.5
offset_top = -26.0
offset_right = 27.5
offset_bottom = -9.0
grow_horizontal = 2
grow_vertical = 2

[node name="HealthLabel" parent="LifeBarContainer/HealthNumberContainer" instance=ExtResource("2_bitmap_display")]
custom_minimum_size = Vector2(32, 14)
layout_mode = 1
offset_top = -2.0
offset_bottom = 2.0
size_preset = 1
preview_text = "100"

[node name="DamageNumberDisplay" parent="LifeBarContainer" instance=ExtResource("2_bitmap_display")]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -20.0
offset_top = -30.0
offset_right = 20.0
offset_bottom = -13.0
grow_horizontal = 2
grow_vertical = 2
z_index = 100
visible = false
size_preset = 1
preview_text = "-30"

[node name="HideTimer" type="Timer" parent="."]
wait_time = 3.0
one_shot = true

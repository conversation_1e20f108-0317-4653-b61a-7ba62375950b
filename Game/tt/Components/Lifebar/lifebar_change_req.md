# 血条组件血量变化数值显示功能需求文档

## 功能概述

为现有的 `LifeBarComponent` 组件添加血量变化数值显示功能，当角色血量发生变化时，在血条上方显示变化的数值，并播放相应的动画效果。

## 功能需求

### 核心功能

#### 1. 数值显示触发
- **触发条件**: 监听 `HealthComponent` 的 `healthDidDecrease` 和 `healthDidIncrease` 信号
- **显示时机**: 血量发生任何变化时立即触发显示
- **数值计算**: 直接使用信号传递的 `difference` 参数

#### 2. 显示内容格式
- **血量减少**: 显示红色文字，格式为 "-30"（负数，包含减号）
- **血量增加**: 显示绿色文字，格式为 "+20"（正数，包含加号）
- **数值范围**: 支持1-9999的数值显示
- **字体**: 使用 `Game/tt/assets/font/方正锐正黑简体_大.TTF` 字体文件

#### 3. 显示位置
- **基准位置**: 血条正上方，水平居中对齐
- **相对定位**: 相对于血条中心点进行偏移计算
- **层级**: 确保在血条之上显示，不被遮挡

### 视觉设计

#### 1. 字体样式
- **默认字号**: 12像素
- **减少血量颜色**: `#FF4444` (红色)
- **增加血量颜色**: `#44FF44` (绿色)
- **字体投影**: 统一的投影效果配置

#### 2. 投影效果配置
- **透明度**: 75%
- **角度**: 138度
- **距离**: 1像素
- **扩展**: 100%
- **大小**: 1像素

### 动画系统

#### 1. 敌人血量减少动画（30帧，1秒@30FPS）
```
帧数    位移(相对血条中心)    缩放      说明
0帧     X=0, Y=0            X=0, Y=0   初始状态（不可见）
3帧     X=0, Y=-10          X=1, Y=1   快速出现并上移
5帧     X=0, Y=0            X=1, Y=1   回到中心位置
15帧    X=0, Y=0            X=1, Y=1   保持显示
20帧    X=0, Y=0            X=0, Y=0   开始消失
```

#### 2. 玩家血量减少动画（30帧，1秒@30FPS）
```
帧数    位移(相对血条中心)    缩放      说明
0帧     X=0, Y=0            X=0, Y=0   初始状态（不可见）
1帧     X=15, Y=-32         X=0, Y=0   快速移动到右上方
3帧     X=45, Y=0           X=1, Y=1   移动到右侧并显示
25帧    X=45, Y=0           X=1, Y=1   保持在右侧显示
30帧    X=45, Y=0           X=0, Y=0   在右侧消失
```

#### 3. 玩家血量增加动画（30帧，1秒@30FPS）
```
帧数    位移(相对血条中心)    缩放      说明
0帧     X=-45, Y=-45        X=0, Y=0   从左上方开始（不可见）
3帧     X=0, Y=0            X=1, Y=1   移动到中心并显示
4帧     X=0, Y=0            X=2, Y=2   短暂放大强调
5帧     X=0, Y=0            X=1, Y=1   恢复正常大小
25帧    X=0, Y=0            X=1, Y=1   保持显示
30帧    X=0, Y=45           X=0, Y=0   向下移动并消失
```

### 配置参数

#### 1. 导出属性
```gdscript
@export var show_damage_numbers: bool = true          # 是否显示血量变化数值
@export var damage_text_font_size: int = 12           # 数值文字大小
@export var damage_color: Color = Color("#FF4444")    # 血量减少颜色
@export var heal_color: Color = Color("#44FF44")      # 血量增加颜色
@export var is_player_character: bool = false         # 是否为玩家角色
```

#### 2. 内部配置
- **动画时长**: 1秒（30帧@30FPS）
- **缓动类型**: 线性插值，精确控制关键帧
- **显示优先级**: 新的变化会重置当前动画

## 技术实现

### 文件修改

#### 1. LifeBarComponent.tscn 场景文件
- 添加 `DamageNumberDisplay` 节点（BitmapNumberDisplay实例）
- 配置节点的初始位置和属性
- 设置适当的层级关系

#### 2. LifeBarComponent.gd 脚本文件
- 添加导出属性和内部变量
- 实现数值显示的核心逻辑
- 创建不同类型的动画序列
- 连接血量变化信号处理

### 核心函数设计

#### 1. 信号处理函数
```gdscript
func on_health_changed(difference: int) -> void
func show_damage_number(value: int) -> void
```

#### 2. 动画控制函数
```gdscript
func play_damage_animation(value: int) -> void
func create_enemy_damage_animation() -> Tween
func create_player_damage_animation() -> Tween
func create_player_heal_animation() -> Tween
```

#### 3. 配置函数
```gdscript
func configure_damage_display() -> void
func update_damage_display_style() -> void
```

### 性能考虑

#### 1. 动画优化
- 使用 Tween 进行高效的属性插值
- 避免频繁的节点创建和销毁
- 复用现有的 BitmapNumberDisplay 节点

#### 2. 内存管理
- 及时清理完成的动画
- 避免动画堆积造成内存泄漏
- 合理控制同时显示的数值数量

#### 3. 渲染优化
- 使用适当的层级避免不必要的重绘
- 优化投影效果的渲染开销
- 在不需要时隐藏数值显示节点

## 集成要求

### 与现有系统的兼容性
- **HealthComponent**: 完全兼容现有的信号系统
- **BitmapNumberDisplay**: 复用现有的数字显示组件
- **血条显示**: 不影响现有的血条功能
- **性能**: 不显著影响现有的渲染性能

### 配置灵活性
- **开关控制**: 可通过 `show_damage_numbers` 完全禁用功能
- **样式定制**: 支持自定义颜色和字体大小
- **角色类型**: 支持不同角色类型的动画效果
- **运行时调整**: 支持运行时修改配置参数

## 测试验证

### 功能测试
- [ ] 血量减少时正确显示负数
- [ ] 血量增加时正确显示正数
- [ ] 不同角色类型播放正确的动画
- [ ] 动画时长和关键帧准确
- [ ] 多次快速变化的处理正确

### 性能测试
- [ ] 大量血量变化时性能稳定
- [ ] 内存使用无明显增长
- [ ] 动画播放流畅无卡顿
- [ ] 与其他UI元素无冲突

### 兼容性测试
- [ ] 与现有血条功能完全兼容
- [ ] 不影响其他组件的正常工作
- [ ] 在不同分辨率下显示正确
- [ ] 支持运行时开关功能

## 验收标准

### 基础功能
- 血量变化时能正确显示数值
- 动画效果符合设计要求
- 颜色和样式配置正确
- 性能影响在可接受范围内

### 用户体验
- 动画流畅自然，无突兀感
- 数值显示清晰易读
- 不干扰游戏主要内容
- 响应及时，无明显延迟

### 技术质量
- 代码结构清晰，易于维护
- 与现有系统集成良好
- 配置灵活，易于定制
- 错误处理完善，稳定可靠

---

**文档版本**: v1.0  
**创建日期**: 2025-01-28  
**预计开发时间**: 2-3天  
**优先级**: 中等
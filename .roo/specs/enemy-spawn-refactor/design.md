# 设计文档

## 概述

简化 enemy_spawns 配置格式，从复杂的生成规则改为直接指定敌人类型和坐标位置。保持向后兼容性，在 EnemySpawner 中添加简单的格式检测和处理逻辑。

## 架构

### 简化方案

```mermaid
graph TD
    A[LevelManager] --> B[WaveManager]
    B --> C[EnemySpawner]
    C --> D[Enemy Instances]
    
    E[level_config.json] --> A
    C --> F[Format Detection]
    F --> G[New Format Handler]
    F --> H[Legacy Format Handler]
```

## 组件和接口

### EnemySpawner 修改

**新增方法：**
```gdscript
func detect_config_format(enemy_spawns: Array) -> String
func process_simplified_config(enemy_spawns: Array) -> void
func create_direct_spawn_task(enemy: String, position: Dictionary) -> Dictionary
```

**修改现有方法：**
```gdscript
func configure_spawn_settings(wave_data: Dictionary) -> void:
    # 添加格式检测逻辑
    # 根据格式调用不同的处理方法
```

## 数据模型

### 新格式（目标）
```json
{
  "enemy_spawns": [
    {
      "enemy": "SkeletonArcher",
      "position": {"x": 100, "y": 100}
    }
  ]
}
```

### 旧格式（兼容）
```json
{
  "enemy_spawns": [
    {
      "enemy_type": "SkeletonArcher",
      "count": 3,
      "spawn_interval": 0.2,
      "spawn_positions": "random_circle"
    }
  ]
}
```

## 错误处理

### 简单验证
```gdscript
func validate_position(position: Dictionary) -> Vector2:
    var x = position.get("x", 0)
    var y = position.get("y", 0)
    return validate_and_adjust_position(Vector2(x, y))
```

### 格式检测
```gdscript
func detect_config_format(enemy_spawns: Array) -> String:
    if enemy_spawns.is_empty():
        return "legacy"
    
    var first_config = enemy_spawns[0]
    if first_config.has("enemy") and first_config.has("position"):
        return "simplified"
    elif first_config.has("enemy_type"):
        return "legacy"
    else:
        return "legacy"  # 默认使用旧格式
```

## 测试策略

### 基础测试
1. 新格式配置解析测试
2. 旧格式兼容性测试
3. 位置验证测试
4. 错误处理测试

## 实施计划

### 单一阶段实施
1. 在 EnemySpawner.configure_spawn_settings() 中添加格式检测
2. 实现新格式的处理逻辑
3. 保持旧格式的现有逻辑不变
4. 添加基础的错误处理和日志
5. 编写测试验证功能
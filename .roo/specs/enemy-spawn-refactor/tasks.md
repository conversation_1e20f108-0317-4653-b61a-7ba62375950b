# 实施计划

## 任务概述

将复杂的 enemy_spawns 配置格式完全替换为简化的坐标格式。直接修改 EnemySpawner.gd 中的配置处理逻辑，移除复杂的位置计算代码。

## 实施任务

- [ ] 1. 简化 configure_spawn_settings 方法
  - 移除旧的复杂配置处理逻辑
  - 直接解析新格式：enemy 和 position 字段
  - 为每个敌人配置创建简单的生成任务
  - _需求: 1.1, 1.2, 4.1_

- [ ] 2. 实现直接坐标位置处理
  - 从配置中直接读取 x, y 坐标
  - 实现 `validate_position()` 方法验证坐标有效性
  - 确保生成位置在地图边界内
  - 保持与玩家的最小安全距离
  - _需求: 2.1, 2.2, 2.3, 2.4_

- [ ] 3. 移除复杂的位置计算代码
  - 删除 `calculate_circle_position()` 相关方法
  - 删除 `calculate_fixed_position()` 相关方法
  - 删除 `calculate_formation_position()` 相关方法
  - 简化 `calculate_spawn_position()` 方法
  - _需求: 4.1, 4.2_

- [ ] 4. 更新生成任务创建逻辑
  - 修改生成任务结构，直接使用坐标
  - 移除 spawn_interval 和 count 相关逻辑
  - 每个敌人配置对应一个生成任务
  - 简化 spawn_queue 的数据结构
  - _需求: 1.1, 4.3_

- [ ] 5. 添加错误处理和调试日志
  - 验证配置格式的正确性
  - 为新格式处理添加调试日志
  - 处理坐标无效的情况
  - 提供清晰的错误信息
  - _需求: 5.1, 5.2, 5.3_

- [ ] 6. 更新配置文件
  - 将 level_config.json 转换为新格式
  - 移除 LevelManager.gd 中的硬编码配置
  - 确保配置文件路径正确
  - _需求: 4.1, 4.2, 4.4_

- [ ] 7. 编写单元测试
  - 测试新格式配置的解析
  - 测试位置验证和边界检查
  - 测试错误处理机制
  - 验证敌人生成的正确性
  - _需求: 5.1_

- [ ] 8. 进行集成测试
  - 在完整关卡中测试新格式
  - 验证敌人生成位置准确性
  - 确保所有信号正常工作
  - 测试性能是否有改善
  - _需求: 4.3_

- [ ] 9. 清理和优化代码
  - 移除不再使用的方法和变量
  - 清理注释和调试代码
  - 确保代码符合项目规范
  - 优化性能和内存使用
  - _需求: 4.1, 4.2_

- [ ] 10. 最终验证
  - 完整的功能测试
  - 确保所有需求都已满足
  - 验证代码质量和性能
  - 准备部署和文档更新
  - _需求: 所有需求_

## 实施注意事项

### 代码修改原则
- 大幅简化现有代码
- 移除不必要的复杂逻辑
- 保持核心接口和信号不变
- 遵循 Comedot 框架的代码规范

### 简化重点
- 直接使用坐标，无需复杂计算
- 移除生成间隔和数量逻辑
- 简化配置验证流程
- 减少代码复杂度

### 性能优化
- 新格式处理应该更快
- 减少内存使用
- 简化生成流程
- 提高配置加载速度
# 需求文档

## 介绍

本功能旨在重构 LevelManager.gd 中的怪物生成逻辑，简化 enemy_spawns 数据结构，提高配置的可读性和维护性。当前的配置结构过于复杂，包含了过多的生成参数，新的结构将采用更直观的位置坐标方式来定义敌人生成。

## 需求

### 需求 1

**用户故事：** 作为游戏开发者，我希望能够使用简化的敌人生成配置，以便更容易地设计和调整关卡中的敌人布局。

#### 验收标准

1. WHEN 配置关卡数据时 THEN 系统 SHALL 支持新的简化 enemy_spawns 数据结构
2. WHEN 使用新配置格式时 THEN 系统 SHALL 能够正确解析 enemy 和 position 字段
3. WHEN 加载关卡配置时 THEN 系统 SHALL 向后兼容旧的配置格式
4. WHEN 生成敌人时 THEN 系统 SHALL 根据指定的精确坐标位置生成敌人

### 需求 2

**用户故事：** 作为游戏设计师，我希望能够精确控制每个敌人的生成位置，以便创建更有策略性的关卡布局。

#### 验收标准

1. WHEN 配置敌人位置时 THEN 系统 SHALL 接受 {x, y} 坐标格式
2. WHEN 生成敌人时 THEN 系统 SHALL 在指定的精确坐标位置生成敌人
3. WHEN 位置超出地图边界时 THEN 系统 SHALL 自动调整到最近的有效位置
4. WHEN 位置与玩家过近时 THEN 系统 SHALL 保持最小安全距离

### 需求 3

**用户故事：** 作为系统维护者，我希望重构后的代码能够保持与现有系统的兼容性，以便不影响现有功能。

#### 验收标准

1. WHEN 使用旧配置格式时 THEN 系统 SHALL 继续正常工作
2. WHEN 切换到新配置格式时 THEN 系统 SHALL 无缝过渡
3. WHEN EnemySpawner 处理新格式时 THEN 系统 SHALL 保持所有现有信号和接口
4. WHEN 生成敌人时 THEN 系统 SHALL 保持现有的敌人属性设置和组管理

### 需求 4

**用户故事：** 作为开发团队成员，我希望新的配置格式更加简洁易读，以便提高开发效率和减少配置错误。

#### 验收标准

1. WHEN 编写配置文件时 THEN 新格式 SHALL 比旧格式减少至少 50% 的配置行数
2. WHEN 查看配置文件时 THEN 敌人位置 SHALL 一目了然
3. WHEN 修改敌人布局时 THEN 只需要修改坐标值即可
4. WHEN 添加新敌人时 THEN 只需要指定敌人类型和位置

### 需求 5

**用户故事：** 作为质量保证工程师，我希望重构后的系统能够提供清晰的错误处理和调试信息。

#### 验收标准

1. WHEN 配置格式错误时 THEN 系统 SHALL 提供明确的错误信息
2. WHEN 敌人生成失败时 THEN 系统 SHALL 记录详细的调试日志
3. WHEN 位置无效时 THEN 系统 SHALL 自动修正并记录警告
4. WHEN 切换配置格式时 THEN 系统 SHALL 记录使用的配置类型

## 配置格式对比

### 当前格式（复杂）
```json
{
  "enemy_spawns": [
    {
      "enemy_type": "SkeletonArcher",
      "count": 3,
      "spawn_interval": 0.2,
      "spawn_positions": "random_circle"
    },
    {
      "enemy_type": "Slime",
      "count": 2,
      "spawn_interval": 0.2,
      "spawn_positions": "fixed_positions"
    }
  ]
}
```

### 目标格式（简化）
```json
{
  "enemy_spawns": [
    {
      "enemy": "SkeletonArcher",
      "position": {"x": 100, "y": 100}
    },
    {
      "enemy": "SkeletonArcher",
      "position": {"x": 150, "y": 150}
    },
    {
      "enemy": "Slime",
      "position": {"x": 200, "y": 200}
    }
  ]
}
```

## 技术约束

1. **向后兼容性**：必须支持现有的配置格式，确保现有关卡不受影响
2. **性能要求**：重构不应显著影响敌人生成的性能
3. **代码规范**：遵循 projectBrief.md 中定义的 Comedot 框架开发规范
4. **信号保持**：保持现有的信号系统和组件间通信机制
5. **调试支持**：保持现有的调试模式和日志输出功能

## 成功标准

1. 新配置格式能够正确生成敌人
2. 旧配置格式继续正常工作
3. 配置文件的可读性和维护性显著提升
4. 所有现有测试继续通过
5. 新功能有完整的单元测试覆盖